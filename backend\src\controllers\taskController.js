const { GenerationTask, User } = require('../models');
const { successResponse, errorResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');
const { Op, Sequelize } = require('sequelize');
const sequelize = require('../models').sequelize;

/**
 * 获取生成任务列表（分页）
 * @route GET /api/admin/tasks
 */
const getTaskList = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      status,
      taskType = 'cover',
      startDate,
      endDate,
      userId
    } = req.query;

    // 构建查询条件
    const where = {};

    // 添加状态过滤
    if (status) {
      where.status = status;
    }

    // 添加任务类型过滤
    if (taskType) {
      where.task_type = taskType;
    }

    // 添加用户ID过滤
    if (userId) {
      where.user_id = userId;
    }

    // 添加日期范围过滤
    if (startDate && endDate) {
      where.created_at = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      where.created_at = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      where.created_at = {
        [Op.lte]: new Date(endDate)
      };
    }

    // 查询总记录数
    const total = await GenerationTask.count({ where });

    // 计算分页参数
    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 查询任务列表，包含用户信息
    const tasks = await GenerationTask.findAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'phone', 'nickname']
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit
    });

    // 恢复状态统计部分
    // 统计任务状态分布
    const statusCounts = await GenerationTask.findAll({
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: where, // 使用与主查询相同的where条件
      group: ['status']
    });

    // 处理统计数据为前端需要的格式
    const stats = {
      total,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      canceled: 0,
      abnormal: 0
    };

    statusCounts.forEach(item => {
      stats[item.status] = parseInt(item.get('count'));
    });

    return successResponse(res, '获取任务列表成功', {
      tasks,
      pagination: {
        total,
        page: parseInt(page),
        pageSize: limit,
        totalPages: Math.ceil(total / limit)
      },
      stats
    });
  } catch (error) {
    logger.error('获取任务列表失败:', error);
    return errorResponse(res, '获取任务列表失败', 500);
  }
};

/**
 * 获取任务详情
 * @route GET /api/admin/tasks/:id
 */
const getTaskDetail = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询单个任务详情
    const task = await GenerationTask.findOne({
      where: { id },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'phone', 'nickname']
        }
      ]
    });

    if (!task) {
      return errorResponse(res, '任务不存在', 404);
    }

    // 查询任务相关的API日志
    const apiLogs = await sequelize.query(
      `SELECT * FROM system_logs 
       WHERE (action = 'api_request' OR action = 'api_response') 
       AND details LIKE :taskId 
       ORDER BY created_at ASC`,
      {
        replacements: { taskId: `%${task.task_id}%` },
        type: sequelize.QueryTypes.SELECT
      }
    );

    // 格式化API日志数据
    let apiRequest = null;
    let apiResponse = null;

    if (apiLogs && apiLogs.length > 0) {
      // 提取请求和响应数据
      apiLogs.forEach(log => {
        try {
          if (log.action === 'api_request') {
            apiRequest = JSON.parse(log.details);
          } else if (log.action === 'api_response') {
            apiResponse = JSON.parse(log.details);
          }
        } catch (e) {
          logger.error(`解析API日志数据失败: ${e.message}`);
        }
      });
    }

    // 如果没有找到API日志，尝试从参数中构建请求数据
    if (!apiRequest && task.parameters) {
      try {
        let parameters = task.parameters;
        if (typeof parameters === 'string') {
          parameters = JSON.parse(parameters);
        }
        
        // 构建基本的API请求数据
        apiRequest = {
          model: parameters.modelName || parameters.model || '未知模型',
          messages: parameters.messages || [],
          options: {}
        };
        
        // 添加常见参数
        ['temperature', 'max_tokens', 'top_p', 'top_k', 'presence_penalty', 
         'frequency_penalty', 'repetition_penalty', 'seed', 'enable_search'].forEach(param => {
          if (parameters[param] !== undefined) {
            apiRequest.options[param] = parameters[param];
          }
        });
      } catch (e) {
        logger.error(`从参数构建API请求数据失败: ${e.message}`);
      }
    }

    return successResponse(res, '获取任务详情成功', { 
      task,
      apiRequest,
      apiResponse
    });
  } catch (error) {
    logger.error(`获取任务详情失败 [ID:${req.params.id}]:`, error);
    return errorResponse(res, '获取任务详情失败', 500);
  }
};

/**
 * 取消任务
 * @route POST /api/admin/tasks/:id/cancel
 */
const cancelTask = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询任务
    const task = await GenerationTask.findOne({
      where: { id }
    });

    if (!task) {
      return errorResponse(res, '任务不存在', 404);
    }

    // 如果任务已经完成或已取消，则返回失败
    if (['completed', 'failed', 'canceled'].includes(task.status)) {
      return errorResponse(res, `无法取消处于 ${task.status} 状态的任务`, 400);
    }

    // 调用取消任务的功能
    const { cancelTask } = require('../utils/aiServiceManager');
    const isCanceled = await cancelTask(task.task_id);

    if (isCanceled) {
      // 更新数据库记录
      task.status = 'canceled';
      task.end_time = new Date();
      if (task.start_time) {
        task.duration_ms = new Date() - new Date(task.start_time);
      }
      await task.save();

      return successResponse(res, '取消任务成功');
    } else {
      return errorResponse(res, '取消任务失败，可能任务已完成或不存在', 400);
    }
  } catch (error) {
    logger.error(`取消任务失败 [ID:${req.params.id}]:`, error);
    return errorResponse(res, '取消任务失败', 500);
  }
};

/**
 * 删除任务记录（仅限已完成或已取消的任务）
 * @route DELETE /api/admin/tasks/:id
 */
const deleteTask = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询任务
    const task = await GenerationTask.findOne({
      where: { id }
    });

    if (!task) {
      return errorResponse(res, '任务不存在', 404);
    }

    // 如果任务正在处理中，则不允许删除
    if (['pending', 'processing'].includes(task.status)) {
      return errorResponse(res, `无法删除处于 ${task.status} 状态的任务，请先取消任务`, 400);
    }

    // 删除任务记录
    await task.destroy();

    return successResponse(res, '删除任务记录成功');
  } catch (error) {
    logger.error(`删除任务记录失败 [ID:${req.params.id}]:`, error);
    return errorResponse(res, '删除任务记录失败', 500);
  }
};

/**
 * 清理任务记录（批量删除过期任务）
 * @route DELETE /api/admin/tasks/cleanup
 */
const cleanupTasks = async (req, res) => {
  try {
    const { olderThan = 30, status = ['completed', 'failed', 'canceled'] } = req.body;

    // 计算截止日期
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThan);

    // 删除符合条件的任务记录
    const { count } = await GenerationTask.destroy({
      where: {
        status: status,
        created_at: {
          [Op.lt]: cutoffDate
        }
      }
    });

    return successResponse(res, `清理任务记录成功，共删除 ${count} 条记录`);
  } catch (error) {
    logger.error('清理任务记录失败:', error);
    return errorResponse(res, '清理任务记录失败', 500);
  }
};

module.exports = {
  getTaskList,
  getTaskDetail,
  cancelTask,
  deleteTask,
  cleanupTasks
};
