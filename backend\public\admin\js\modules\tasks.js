/**
 * 任务管理模块
 * 用于显示和管理封面生成任务
 * 依赖utils.js中的getAuthHeaders函数进行API请求认证
 */

window.taskModule = (function() {
  // 模块内部变量
  let currentPage = 1;
  let pageSize = 10;
  let total = 0;
  let taskStatus = '';
  let startDate = '';
  let endDate = '';
  let userId = '';

  // 初始化函数
  function init() {
    console.log('初始化任务管理模块...');
    bindEvents();
    loadTaskList();
  }

  // 绑定事件
  function bindEvents() {
    // 搜索按钮点击事件
    document.getElementById('taskSearchBtn').addEventListener('click', function() {
      currentPage = 1;
      collectFilterValues();
      loadTaskList();
    });

    // 重置按钮点击事件
    document.getElementById('taskResetBtn').addEventListener('click', function() {
      resetFilters();
      loadTaskList();
    });

    // 清理记录按钮点击事件
    document.getElementById('taskCleanupBtn').addEventListener('click', function() {
      showCleanupModal();
    });

    // 确认清理按钮点击事件
    document.getElementById('confirmTaskCleanupBtn').addEventListener('click', function() {
      cleanupTasks();
    });
  }

  // 收集过滤条件值
  function collectFilterValues() {
    taskStatus = document.getElementById('taskStatusFilter').value;
    startDate = document.getElementById('taskStartDate').value;
    endDate = document.getElementById('taskEndDate').value;
    userId = document.getElementById('taskUserIdFilter').value;
  }

  // 重置过滤条件
  function resetFilters() {
    document.getElementById('taskStatusFilter').value = '';
    document.getElementById('taskStartDate').value = '';
    document.getElementById('taskEndDate').value = '';
    document.getElementById('taskUserIdFilter').value = '';

    taskStatus = '';
    startDate = '';
    endDate = '';
    userId = '';
    currentPage = 1;
  }

  // 加载任务列表
  function loadTaskList() {
    const tableBody = document.getElementById('taskTableBody');
    tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div></td></tr>';

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('未找到token，无法加载任务列表');
      return;
    }

    // 构建查询参数
    const params = new URLSearchParams();
    params.append('page', currentPage);
    params.append('pageSize', pageSize);

    if (taskStatus) params.append('status', taskStatus);
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    if (userId) params.append('userId', userId);

    // 获取任务列表数据
    fetch(`/api/admin/tasks?${params.toString()}`, {
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (!data.success) {
        console.error('获取任务列表失败:', data.message);
        tableBody.innerHTML = `<tr><td colspan="9" class="text-center py-3 text-danger">加载失败: ${data.message || '未知错误'}</td></tr>`;
        return;
      }

      // 更新统计数据
      updateStatistics(data.data.stats);

      // 更新分页信息
      total = data.data.pagination.total;
      updatePagination(data.data.pagination);

      // 更新任务列表
      renderTaskList(data.data.tasks);
    })
    .catch(error => {
      console.error('获取任务列表出错:', error);
      tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-3 text-danger">加载失败，请检查网络连接</td></tr>';
    });
  }

  // 更新统计数据
  function updateStatistics(stats) {
    document.getElementById('taskTotalCount').textContent = stats.total || 0;
    document.getElementById('taskPendingCount').textContent = stats.pending || 0;
    document.getElementById('taskProcessingCount').textContent = stats.processing || 0;
    document.getElementById('taskCompletedCount').textContent = stats.completed || 0;
    document.getElementById('taskFailedCount').textContent = stats.failed || 0;
    document.getElementById('taskCanceledCount').textContent = stats.canceled || 0;
  }

  // 渲染任务列表
  function renderTaskList(tasks) {
    const tableBody = document.getElementById('taskTableBody');

    if (!tasks || tasks.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-3">暂无任务记录</td></tr>';
      return;
    }

    let html = '';
    tasks.forEach(task => {
      html += `
        <tr>
          <td>${task.id}</td>
          <td><span class="task-id-truncated" title="${task.task_id}">${truncateText(task.task_id, 12)}</span></td>
          <td>${task.user ? (task.user.nickname || task.user.username) : '游客'}</td>
          <td>${getTaskTypeText(task.task_type)}</td>
          <td>${getStatusBadge(task.status)}</td>
          <td>${task.cover_type_name || '未知'}</td>
          <td>${task.style_name || '未知'}</td>
          <td>
            <span title="${formatDateTime(task.createdAt || task.created_at)}">
              ${formatTimeAgo(task.createdAt || task.created_at)}
            </span>
          </td>
          <td>
            <div class="btn-group btn-group-sm">
              <button type="button" class="btn btn-outline-primary" onclick="window.taskModule.viewTaskDetail(${task.id})">
                <i class="bi bi-eye"></i> 详情
              </button>
              ${getActionButton(task)}
            </div>
          </td>
        </tr>
      `;
    });

    tableBody.innerHTML = html;
  }

  // 获取任务操作按钮
  function getActionButton(task) {
    let buttons = '';
    
    // 添加记录按钮（所有任务都显示此按钮）
    buttons += `
      <button type="button" class="btn btn-outline-info" onclick="window.taskModule.viewTaskApiRecord(${task.id})">
        <i class="bi bi-file-text"></i> 记录
      </button>
    `;
    
    if (['pending', 'processing'].includes(task.status)) {
      buttons += `
        <button type="button" class="btn btn-outline-danger" onclick="window.taskModule.cancelTask(${task.id})">
          <i class="bi bi-x-circle"></i> 取消
        </button>
      `;
    }

    if (['completed', 'failed', 'canceled'].includes(task.status)) {
      buttons += `
        <button type="button" class="btn btn-outline-danger" onclick="window.taskModule.deleteTask(${task.id})">
          <i class="bi bi-trash"></i> 删除
        </button>
      `;
    }

    return buttons;
  }

  // 获取任务类型文本
  function getTaskTypeText(type) {
    const typeMap = {
      'cover': '封面生成',
      'article': '文章生成',
      'default': '未知类型'
    };
    return typeMap[type] || typeMap.default;
  }

  // 获取状态徽章HTML
  function getStatusBadge(status) {
    const statusConfig = {
      'pending': { color: 'secondary', icon: 'hourglass', text: '等待中' },
      'processing': { color: 'primary', icon: 'arrow-repeat', text: '处理中' },
      'completed': { color: 'success', icon: 'check-circle', text: '已完成' },
      'failed': { color: 'danger', icon: 'x-circle', text: '失败' },
      'canceled': { color: 'warning', icon: 'slash-circle', text: '已取消' },
      'abnormal': { color: 'dark', icon: 'exclamation-triangle', text: '非正常' }
    };

    const config = statusConfig[status] || statusConfig.pending;

    return `
      <span class="badge bg-${config.color} task-status">
        <i class="bi bi-${config.icon}"></i> ${config.text}
      </span>
    `;
  }

  // 更新分页控件
  function updatePagination(pagination) {
    const paginationEl = document.getElementById('taskPagination');

    const totalPages = pagination.totalPages;
    currentPage = pagination.page;

    let html = '';

    // 上一页按钮
    html += `
      <li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="return window.taskModule.gotoPage(${currentPage - 1})">
          上一页
        </a>
      </li>
    `;

    // 计算显示的页码范围
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    if (endPage - startPage < 4 && totalPages > 5) {
      startPage = Math.max(1, endPage - 4);
    }

    // 第一页
    if (startPage > 1) {
      html += `
        <li class="page-item">
          <a class="page-link" href="#" onclick="return window.taskModule.gotoPage(1)">1</a>
        </li>
      `;

      if (startPage > 2) {
        html += '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
      }
    }

    // 页码按钮
    for (let i = startPage; i <= endPage; i++) {
      html += `
        <li class="page-item ${i === currentPage ? 'active' : ''}">
          <a class="page-link" href="#" onclick="return window.taskModule.gotoPage(${i})">${i}</a>
        </li>
      `;
    }

    // 最后一页
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        html += '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
      }

      html += `
        <li class="page-item">
          <a class="page-link" href="#" onclick="return window.taskModule.gotoPage(${totalPages})">${totalPages}</a>
        </li>
      `;
    }

    // 下一页按钮
    html += `
      <li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="return window.taskModule.gotoPage(${currentPage + 1})">
          下一页
        </a>
      </li>
    `;

    paginationEl.innerHTML = html;

    // 清除之前的分页信息
    const existingPaginationInfo = paginationEl.parentNode.querySelector('.pagination-info');
    if (existingPaginationInfo) {
      existingPaginationInfo.remove();
    }

    // 添加分页信息
    const paginationInfo = document.createElement('div');
    paginationInfo.className = 'pagination-info';
    paginationInfo.textContent = `共 ${pagination.total} 条记录，当前第 ${pagination.page}/${totalPages} 页`;
    paginationEl.parentNode.insertBefore(paginationInfo, paginationEl);
  }

  // 跳转到指定页码
  function gotoPage(page) {
    if (page < 1 || page > Math.ceil(total / pageSize)) {
      return false;
    }

    currentPage = page;
    loadTaskList();
    return false; // 阻止默认行为
  }

  // 查看任务详情
  function viewTaskDetail(taskId) {
    resetTaskDetailModal();

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('未找到token，无法加载任务详情');
      return;
    }

    // 加载详情
    fetch(`/api/admin/tasks/${taskId}`, {
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (!data.success) {
        console.error('获取任务详情失败:', data.message);
        alert('获取任务详情失败: ' + (data.message || '未知错误'));
        return;
      }

      const task = data.data.task;

      // 更新详情模态框
      document.getElementById('taskDetailId').textContent = `任务 #${task.id}`;
      document.getElementById('taskDetailStatus').innerHTML = getStatusBadge(task.status);
      document.getElementById('taskDetailTaskId').textContent = task.task_id;
      document.getElementById('taskDetailType').textContent = getTaskTypeText(task.task_type);
      document.getElementById('taskDetailUser').textContent = task.user ?
        `${task.user.nickname || '未设置昵称'} (${task.user.phone})` : '游客';
      document.getElementById('taskDetailStartTime').textContent = formatDateTime(task.start_time);
      document.getElementById('taskDetailEndTime').textContent = task.end_time ? formatDateTime(task.end_time) : '-';
      document.getElementById('taskDetailDuration').textContent = task.duration_ms ?
        formatDuration(task.duration_ms) : '-';

      // 处理模型名称
      let modelName = '-';
      if (task.parameters) {
        try {
          let parameters = task.parameters;
          if (typeof parameters === 'string') {
            parameters = JSON.parse(parameters);
          }
          modelName = parameters.serviceName || '-';
        } catch (e) {
          console.error('解析任务参数获取模型名称失败:', e);
        }
      }
      document.getElementById('taskDetailModelName').textContent = modelName;

      // 显示使用的封面类型和风格
      document.getElementById('taskDetailCoverType').textContent = task.cover_type_name || '未知';
      document.getElementById('taskDetailStyleName').textContent = task.style_name || '未知';

      document.getElementById('taskDetailResultId').textContent = task.result_id || '-';

      // 处理错误信息
      if (task.error_message) {
        document.getElementById('taskDetailErrorSection').classList.remove('d-none');
        document.getElementById('taskDetailError').textContent = task.error_message;
      } else {
        document.getElementById('taskDetailErrorSection').classList.add('d-none');
      }

      // 处理任务参数
      if (task.parameters) {
        let parameters = task.parameters;
        try {
          if (typeof parameters === 'string') {
            parameters = JSON.parse(parameters);
          }
          document.getElementById('taskDetailParams').textContent = JSON.stringify(parameters, null, 2);
        } catch (e) {
          document.getElementById('taskDetailParams').textContent = parameters;
        }
      } else {
        document.getElementById('taskDetailParams').textContent = '无参数';
      }

      // 更新操作按钮
      const cancelBtn = document.getElementById('taskDetailCancelBtn');

      if (['pending', 'processing'].includes(task.status)) {
        cancelBtn.classList.remove('d-none');
        cancelBtn.onclick = function() {
          cancelTask(task.id, true);
        };
      } else {
        cancelBtn.classList.add('d-none');
      }

      // 显示模态框
      const modal = new bootstrap.Modal(document.getElementById('taskDetailModal'));
      modal.show();
    })
    .catch(error => {
      console.error('获取任务详情出错:', error);
      alert('获取任务详情失败，请稍后再试');
    });
  }

  // 重置任务详情模态框
  function resetTaskDetailModal() {
    document.getElementById('taskDetailId').textContent = '';
    document.getElementById('taskDetailStatus').innerHTML = '';

    document.getElementById('taskDetailTaskId').textContent = '';
    document.getElementById('taskDetailType').textContent = '';
    document.getElementById('taskDetailUser').textContent = '';

    document.getElementById('taskDetailStartTime').textContent = '';
    document.getElementById('taskDetailEndTime').textContent = '';
    document.getElementById('taskDetailDuration').textContent = '';
    document.getElementById('taskDetailModelName').textContent = '';
    document.getElementById('taskDetailCoverType').textContent = '';
    document.getElementById('taskDetailStyleName').textContent = '';
    document.getElementById('taskDetailResultId').textContent = '';

    document.getElementById('taskDetailErrorSection').classList.add('d-none');
    document.getElementById('taskDetailError').textContent = '';

    document.getElementById('taskDetailParams').textContent = '';

    document.getElementById('taskDetailCancelBtn').classList.add('d-none');
    document.getElementById('taskDetailCancelBtn').onclick = null;
  }

  // 取消任务
  function cancelTask(taskId, fromDetail = false) {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('未找到token，无法取消任务');
      return;
    }

    if (!confirm('确定要取消这个任务吗？此操作无法撤销。')) {
      return;
    }

    // 发送取消请求
    fetch(`/api/admin/tasks/${taskId}/cancel`, {
      method: 'POST',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('任务取消成功');

        // 如果在详情页，关闭模态框并刷新任务列表
        if (fromDetail) {
          const modal = bootstrap.Modal.getInstance(document.getElementById('taskDetailModal'));
          if (modal) {
            modal.hide();
          }
        }

        // 刷新任务列表
        loadTaskList();
      } else {
        alert('取消任务失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('取消任务请求失败:', error);
      alert('取消任务请求失败，请稍后再试');
    });
  }

  // 删除任务记录
  function deleteTask(taskId, fromDetail = false) {
    // 永远通过模态框确认删除，不允许直接删除
      document.getElementById('deleteTaskId').value = taskId;
      document.getElementById('deleteTaskIdDisplay').textContent = taskId;

      const modal = new bootstrap.Modal(document.getElementById('deleteTaskModal'));
      modal.show();

      // 绑定确认按钮事件
      document.getElementById('confirmDeleteTaskBtn').onclick = function() {
        performDeleteTask(taskId, fromDetail);
        modal.hide();
      };
  }

  // 执行删除任务操作
  function performDeleteTask(taskId, fromDetail) {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('未找到token，无法删除任务');
      return;
    }

    fetch(`/api/admin/tasks/${taskId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('任务记录删除成功');

        // 如果在详情页，关闭模态框
        if (fromDetail) {
          const modal = bootstrap.Modal.getInstance(document.getElementById('taskDetailModal'));
          if (modal) {
            modal.hide();
          }
        }

        // 刷新任务列表
        loadTaskList();
      } else {
        alert('删除任务记录失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('删除任务记录请求失败:', error);
      alert('删除任务记录请求失败，请稍后再试');
    });
  }

  // 显示清理确认模态框
  function showCleanupModal() {
    const modal = new bootstrap.Modal(document.getElementById('taskCleanupModal'));
    modal.show();
  }

  // 清理任务记录
  function cleanupTasks() {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('未找到token，无法清理任务记录');
      return;
    }

    const olderThan = document.getElementById('taskCleanupDays').value;

    // 显示按钮加载状态
    const confirmBtn = document.getElementById('confirmTaskCleanupBtn');
    const originalText = confirmBtn.textContent;
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';

    fetch('/api/admin/tasks/cleanup', {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        olderThan: parseInt(olderThan),
        status: ['completed', 'failed', 'canceled']
      })
    })
    .then(response => response.json())
    .then(data => {
      // 恢复按钮状态
      confirmBtn.disabled = false;
      confirmBtn.textContent = originalText;

      if (data.success) {
        alert('任务记录清理成功');

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('taskCleanupModal'));
        if (modal) {
          modal.hide();
        }

        // 刷新任务列表
        loadTaskList();
      } else {
        alert('清理任务记录失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('清理任务记录请求失败:', error);

      // 恢复按钮状态
      confirmBtn.disabled = false;
      confirmBtn.textContent = originalText;

      alert('清理任务记录请求失败，请稍后再试');
    });
  }

  // 工具函数 - 截断文本
  function truncateText(text, length) {
    if (!text) return '';
    return text.length > length ? text.substring(0, length) + '...' : text;
  }

  // 工具函数 - 格式化日期时间
  function formatDateTime(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleString();
  }

  // 工具函数 - 格式化持续时间
  function formatDuration(ms) {
    if (!ms) return '';

    if (ms < 1000) {
      return `${ms}毫秒`;
    } else if (ms < 60000) {
      return `${(ms / 1000).toFixed(2)}秒`;
    } else {
      const minutes = Math.floor(ms / 60000);
      const seconds = ((ms % 60000) / 1000).toFixed(2);
      return `${minutes}分${seconds}秒`;
    }
  }

  // 工具函数 - 计算多久以前
  function formatTimeAgo(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();

    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
      return `${diffSec}秒前`;
    } else if (diffMin < 60) {
      return `${diffMin}分钟前`;
    } else if (diffHour < 24) {
      return `${diffHour}小时前`;
    } else if (diffDay < 30) {
      return `${diffDay}天前`;
    } else {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month}月${day}日`;
    }
  }

  // 查看任务API记录
  function viewTaskApiRecord(taskId) {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('未找到token，无法加载任务API记录');
      return;
    }

    // 加载API记录
    fetch(`/api/admin/tasks/${taskId}`, {
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (!data.success) {
        console.error('获取任务API记录失败:', data.message);
        alert('获取任务API记录失败: ' + (data.message || '未知错误'));
        return;
      }

      const task = data.data.task;
      
      // 获取API请求和响应数据
      const apiRequest = data.data.apiRequest || {};
      const apiResponse = data.data.apiResponse || {};
      
      // 创建模态框（如果不存在）
      let apiRecordModal = document.getElementById('taskApiRecordModal');
      if (!apiRecordModal) {
        const modalHTML = `
          <div class="modal fade" id="taskApiRecordModal" tabindex="-1" aria-labelledby="taskApiRecordModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-scrollable">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="taskApiRecordModalLabel">任务API记录</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <ul class="nav nav-tabs" id="apiRecordTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                      <button class="nav-link active" id="api-request-tab" data-bs-toggle="tab" data-bs-target="#api-request" type="button" role="tab" aria-controls="api-request" aria-selected="true">API请求</button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link" id="api-response-tab" data-bs-toggle="tab" data-bs-target="#api-response" type="button" role="tab" aria-controls="api-response" aria-selected="false">API响应</button>
                    </li>
                  </ul>
                  <div class="tab-content p-3" id="apiRecordTabContent">
                    <div class="tab-pane fade show active" id="api-request" role="tabpanel" aria-labelledby="api-request-tab">
                      <div class="d-flex justify-content-end mb-2">
                        <button class="btn btn-sm btn-outline-secondary" id="copyApiRequestBtn">
                          <i class="bi bi-clipboard"></i> 复制请求数据
                        </button>
                      </div>
                      <pre id="apiRequestContent" class="bg-light p-3" style="max-height: 500px; overflow: auto; white-space: pre-wrap; word-wrap: break-word; word-break: break-all;"></pre>
                    </div>
                    <div class="tab-pane fade" id="api-response" role="tabpanel" aria-labelledby="api-response-tab">
                      <div class="d-flex justify-content-end mb-2">
                        <button class="btn btn-sm btn-outline-secondary" id="copyApiResponseBtn">
                          <i class="bi bi-clipboard"></i> 复制响应数据
                        </button>
                      </div>
                      <pre id="apiResponseContent" class="bg-light p-3" style="max-height: 500px; overflow: auto; white-space: pre-wrap; word-wrap: break-word; word-break: break-all;"></pre>
                    </div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
              </div>
            </div>
          </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        apiRecordModal = document.getElementById('taskApiRecordModal');
        
        // 添加复制按钮功能
        document.getElementById('copyApiRequestBtn').addEventListener('click', function() {
          const content = document.getElementById('apiRequestContent').textContent;
          navigator.clipboard.writeText(content)
            .then(() => alert('API请求数据已复制到剪贴板'))
            .catch(err => console.error('复制失败:', err));
        });
        
        document.getElementById('copyApiResponseBtn').addEventListener('click', function() {
          const content = document.getElementById('apiResponseContent').textContent;
          navigator.clipboard.writeText(content)
            .then(() => alert('API响应数据已复制到剪贴板'))
            .catch(err => console.error('复制失败:', err));
        });
      }
      
      // 处理请求数据 - 格式化为友好显示
      let requestContent = {};
      
      if (apiRequest) {
        if (apiRequest.taskId) {
          // 来自系统日志的格式
          requestContent = {
            model: apiRequest.model || '未知',
            messages: apiRequest.messages || [],
            options: apiRequest.options || {}
          };
        } else {
          // 后端构建的格式
          requestContent = apiRequest;
        }
      } else if (task.parameters) {
        // 尝试从任务参数中获取
        try {
          let parameters = task.parameters;
          if (typeof parameters === 'string') {
            parameters = JSON.parse(parameters);
          }
          requestContent = {
            model: parameters.modelName || parameters.model || '未知',
            serviceId: parameters.serviceId,
            serviceName: parameters.serviceName,
            ...parameters
          };
        } catch (e) {
          requestContent = { error: '无法解析任务参数', raw: task.parameters };
        }
      } else {
        requestContent = { message: '未找到API请求数据' };
      }
      
      // 处理响应数据
      let responseContent = {};
      
      if (apiResponse) {
        if (apiResponse.taskId) {
          // 来自系统日志的格式
          responseContent = apiResponse.response || { message: '空响应' };
        } else {
          // 其他格式
          responseContent = apiResponse;
        }
      } else if (task.error_message) {
        // 错误信息
        responseContent = { 
          error: task.error_message,
          status: 'error'
        };
      } else {
        responseContent = { message: '未找到API响应数据' };
      }
      
      // 填充内容
      document.getElementById('apiRequestContent').textContent = JSON.stringify(requestContent, null, 2);
      document.getElementById('apiResponseContent').textContent = JSON.stringify(responseContent, null, 2);
      
      // 显示模态框
      const modal = new bootstrap.Modal(apiRecordModal);
      modal.show();
    })
    .catch(error => {
      console.error('获取任务API记录出错:', error);
      alert('获取任务API记录失败，请稍后再试');
    });
  }

  // 导出公共方法
  return {
    init: init,
    gotoPage: gotoPage,
    viewTaskDetail: viewTaskDetail,
    cancelTask: cancelTask,
    deleteTask: deleteTask,
    viewTaskApiRecord: viewTaskApiRecord
  };
})();
