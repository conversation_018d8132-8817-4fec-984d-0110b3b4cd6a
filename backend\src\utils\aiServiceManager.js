const axios = require('axios');
const { AIServiceConfig, GenerationTask } = require('../models');
const { Op } = require('sequelize');
const logger = require('./logger');
const { decryptApiKey } = require('./encryption');
const apiKeyPoolManager = require('./apiKeyPoolManager');

// 任务映射表，存储任务ID和对应的AbortController
const activeTasksMap = new Map();

// 任务超时时间（毫秒）
const TASK_TIMEOUT = 180000; // 3分钟

// 定期清理僵尸任务的间隔时间（毫秒）
const CLEANUP_INTERVAL = 60000; // 1分钟

// 任务最大存活时间（毫秒），超过此时间的任务将被强制清理
const MAX_TASK_LIFETIME = 300000; // 5分钟

// 内存使用阈值（字节），超过此阈值将主动触发垃圾回收
const MEMORY_THRESHOLD = 500 * 1024 * 1024; // 500MB

// 连续请求计数器
let consecutiveRequestCount = 0;

// 最大连续请求数，超过此数量将尝试主动触发垃圾回收
const GC_TRIGGER_THRESHOLD = 2; // 连续2次请求后触发

// 存储任务创建时间
const taskCreationTime = new Map();

/**
 * 获取当前激活的AI服务
 */
const getActiveAIService = async () => {
  try {
    // 查找当前激活的AI服务
    const activeService = await AIServiceConfig.findOne({
      where: { is_active: true }
    });

    if (!activeService) {
      logger.warn('没有找到激活的AI服务，将尝试激活第一个可用服务');

      // 如果没有激活的服务，尝试获取并激活第一个服务
      const firstService = await AIServiceConfig.findOne();

      if (!firstService) {
        throw new Error('系统中没有配置AI服务');
      }

      // 激活第一个服务
      firstService.is_active = true;
      await firstService.save();

      logger.info(`已自动激活服务: ${firstService.service_name}`);

      return {
        id: firstService.id,
        serviceName: firstService.service_name,
        baseUrl: firstService.base_url,
        apiKey: await decryptApiKey(firstService.api_key.startsWith('"') && firstService.api_key.endsWith('"') ? JSON.parse(firstService.api_key) : firstService.api_key),
        modelName: firstService.model_name,
        requestFormat: firstService.request_format ? JSON.parse(firstService.request_format) : null,
        responseFormat: firstService.response_format ? JSON.parse(firstService.response_format) : null
      };
    }

    // 解密API密钥
    let apiKeyData = activeService.api_key;

    // 检查是否是JSON字符串
    try {
      if (apiKeyData.startsWith('"') && apiKeyData.endsWith('"')) {
        // 去除JSON字符串的引号
        apiKeyData = JSON.parse(apiKeyData);
      }
    } catch (e) {
      logger.warn('API密钥格式解析失败，将使用原始格式:', e);
    }

    const apiKey = await decryptApiKey(apiKeyData);

    return {
      id: activeService.id,
      serviceName: activeService.service_name,
      baseUrl: activeService.base_url,
      apiKey: apiKey,
      modelName: activeService.model_name,
      requestFormat: activeService.request_format ? JSON.parse(activeService.request_format) : null,
      responseFormat: activeService.response_format ? JSON.parse(activeService.response_format) : null,
      parameters: activeService.parameters || null
    };
  } catch (error) {
    logger.error('获取当前激活的AI服务失败:', error);
    throw new Error('获取AI服务配置失败');
  }
};

/**
 * 取消指定任务ID的生成任务
 * @param {string} taskId - 任务ID
 * @param {string} [reason] - 取消原因
 * @returns {boolean} - 是否成功取消
 */
const cancelTask = async (taskId, reason = '用户请求取消') => {
  try {
    logger.info(`开始处理取消任务请求: ${taskId}，原因: ${reason}`);

    // 首先检查内存中的活动任务
    if (!taskId) {
      logger.warn(`取消任务失败: 任务ID为空`);
      return false;
    }

    // 检查内存中是否有此任务
    const hasActiveTask = activeTasksMap.has(taskId);
    logger.info(`任务${taskId}在内存中${hasActiveTask ? '存在' : '不存在'}`);

    // 记录当前活跃任务数量
    logger.info(`当前活跃任务数量: ${activeTasksMap.size}`);
    if (activeTasksMap.size > 0) {
      logger.info(`活跃任务列表: ${Array.from(activeTasksMap.keys()).join(', ')}`);
    }

    // 无论任务是否在内存中，都先更新数据库中的任务状态
    let taskRecord = null;
    try {
      taskRecord = await GenerationTask.findOne({
        where: { task_id: taskId }
      });

      if (taskRecord) {
        logger.info(`找到数据库中的任务记录: ${taskId}，当前状态: ${taskRecord.status}`);

        // 只有当任务状态为processing时才更新
        if (taskRecord.status === 'processing') {
          taskRecord.status = 'canceled';
          taskRecord.end_time = new Date();
          if (taskRecord.start_time) {
            taskRecord.duration_ms = new Date() - new Date(taskRecord.start_time);
          }
          taskRecord.error_message = '用户取消任务';

          // 记录取消原因
          const existingParams = JSON.parse(taskRecord.parameters || '{}');
          taskRecord.parameters = JSON.stringify({
            ...existingParams,
            cancelReason: reason,
            cancelTime: new Date().toISOString(),
            cancelSource: hasActiveTask ? 'memory_task' : 'database_only',
            forceTerminated: true // 标记为强制终止
          });

          await taskRecord.save();
          logger.info(`成功更新数据库中任务状态: ${taskId} -> canceled`);
        } else {
          logger.info(`任务${taskId}状态为${taskRecord.status}，无需更新状态`);
        }
      } else {
        logger.warn(`未找到数据库中的任务记录: ${taskId}`);
      }
    } catch (dbError) {
      logger.error(`更新任务记录状态失败: ${taskId}`, dbError);
    }

    // 如果任务不在内存中，但在数据库中存在且已更新状态，则认为取消成功
    if (!hasActiveTask) {
      if (taskRecord && taskRecord.status === 'canceled') {
        logger.info(`成功取消数据库中的任务: ${taskId}，原因: ${reason}`);

        // 尝试查找同一用户的其他活跃任务并取消它们
        if (taskRecord.user_id) {
          try {
            // 查找同一用户的其他处理中的任务
            const otherTasks = await GenerationTask.findAll({
              where: {
                user_id: taskRecord.user_id,
                status: 'processing',
                task_id: { [Op.ne]: taskId } // 排除当前任务
              }
            });

            if (otherTasks.length > 0) {
              logger.info(`找到用户 ${taskRecord.user_id} 的其他 ${otherTasks.length} 个处理中任务，尝试取消`);

              // 遍历取消每个任务
              for (const task of otherTasks) {
                // 递归调用自身，但避免无限循环
                await cancelTask(task.task_id, `系统自动取消: 用户 ${taskRecord.user_id} 的任务 ${taskId} 被取消`);
              }
            }
          } catch (error) {
            logger.error(`尝试取消用户 ${taskRecord.user_id} 的其他任务失败:`, error);
          }
        }

        return true;
      }
      return false;
    }

    // 获取控制器并中止请求
    const controller = activeTasksMap.get(taskId);
    logger.info(`准备中止任务: ${taskId}`);
    controller.abort();
    logger.info(`已中止任务: ${taskId}`);

    // 清除任务超时计时器
    if (controller.timeoutId) {
      clearTimeout(controller.timeoutId);
      logger.info(`已清除任务 ${taskId} 的超时计时器`);
    }

    // 从活跃任务映射表中移除
    activeTasksMap.delete(taskId);
    logger.info(`已从活跃任务映射表中移除任务: ${taskId}`);
    logger.info(`移除后活跃任务数量: ${activeTasksMap.size}`);

    // 尝试查找同一用户的其他活跃任务并取消它们
    if (taskRecord && taskRecord.user_id) {
      try {
        // 查找同一用户的其他处理中的任务
        const otherTasks = await GenerationTask.findAll({
          where: {
            user_id: taskRecord.user_id,
            status: 'processing',
            task_id: { [Op.ne]: taskId } // 排除当前任务
          }
        });

        if (otherTasks.length > 0) {
          logger.info(`找到用户 ${taskRecord.user_id} 的其他 ${otherTasks.length} 个处理中任务，尝试取消`);

          // 遍历取消每个任务
          for (const task of otherTasks) {
            // 递归调用自身，但避免无限循环
            await cancelTask(task.task_id, `系统自动取消: 用户 ${taskRecord.user_id} 的任务 ${taskId} 被取消`);
          }
        }
      } catch (error) {
        logger.error(`尝试取消用户 ${taskRecord.user_id} 的其他任务失败:`, error);
      }
    }

    logger.info(`成功取消任务: ${taskId}，原因: ${reason}`);
    return true;
  } catch (error) {
    logger.error(`取消任务失败: ${taskId}，原因: ${error.message}`, error);

    // 尝试记录更多错误信息
    try {
      logger.error(`取消任务失败详情: ${taskId}`, {
        errorName: error.name,
        errorMessage: error.message,
        errorStack: error.stack,
        hasActiveTask: activeTasksMap.has(taskId)
      });
    } catch (logError) {
      logger.error(`记录取消任务错误详情失败: ${taskId}`);
    }

    return false;
  }
};

/**
 * 获取活跃任务数量
 * @returns {number} - 当前活跃任务数
 */
const getActiveTasksCount = () => {
  return activeTasksMap.size;
};

/**
 * 调用AI服务生成内容
 * @param {Object} params - 调用参数
 * @param {Array} params.messages - 消息数组
 * @param {Object} params.options - 其他选项
 * @param {string} [params.taskId] - 任务ID，用于后续取消任务
 * @param {number} [params.userId] - 用户ID，用于记录任务
 * @param {Object} [params.serviceOverride] - 直接使用的服务配置，用于测试时覆盖从池获取的服务
 */
const generateContent = async (params) => {
  // 检查是否有参数冲突
  if (params.options) {
    // 预先处理豆包API的参数冲突问题
    if (params.serviceOverride && 
        (params.serviceOverride.serviceName.includes('doubao') || params.serviceOverride.serviceName.includes('豆包') || 
         params.serviceOverride.modelName.includes('doubao') || params.serviceOverride.modelName.includes('豆包'))) {
      
      if (params.options.max_tokens !== undefined && params.options.max_completion_tokens !== undefined) {
        logger.warn(`generateContent入口检测到豆包API参数冲突：max_tokens和max_completion_tokens不能同时设置，已自动移除max_tokens参数`);
        delete params.options.max_tokens;
      }
    }
  }

  // 创建AbortController用于取消请求
  const controller = new AbortController();
  const { signal } = controller;

  // 在任务映射表中存储任务信息，便于后续取消
  if (params.taskId) {
    activeTasksMap.set(params.taskId, controller);
    // 记录任务创建时间
    taskCreationTime.set(params.taskId, Date.now());
  }

  // 记录任务开始的服务信息
  let serviceInfo = null;
  let keyId = null;

  // 重试计数器
  let retryCount = 0;
  const MAX_RETRIES = 2; // 最大重试次数

  // 任务创建标志，确保任务只被创建一次
  let taskCreated = false;

  // 函数：创建或更新任务记录
  const createOrUpdateTask = async () => {
    if (!params.taskId) return null;

    try {
      // 检查任务是否已存在
      let taskRecord = await GenerationTask.findOne({
        where: { task_id: params.taskId }
      });

      // 准备任务参数
      const taskParameters = {
        messageCount: params.messages?.length || 0,
        retry: retryCount
      };

      // 记录所有API参数
      if (params.options) {
        taskParameters.options = params.options;
      }

      // 如果传入了parameters，合并它
      if (params.parameters) {
        if (typeof params.parameters === 'string') {
          try {
            Object.assign(taskParameters, JSON.parse(params.parameters));
          } catch (e) {
            logger.warn(`无法解析任务parameters JSON: ${e.message}`);
            taskParameters.rawParameters = params.parameters;
          }
        } else {
          Object.assign(taskParameters, params.parameters);
        }
      }

      // 添加服务信息（如果已获取）
      if (serviceInfo) {
        taskParameters.serviceInfo = {
          id: serviceInfo.id,
          name: serviceInfo.serviceName,
          model: serviceInfo.modelName
        };
      }

      if (!taskRecord) {
        // 创建新任务记录
        taskRecord = await GenerationTask.create({
          task_id: params.taskId,
          user_id: params.userId || null,
          status: 'processing',
          task_type: 'cover',
          start_time: new Date(),
          parameters: JSON.stringify(taskParameters)
        });
        logger.info(`已创建任务记录: ${params.taskId}`);
      } else if (!taskCreated) {
        // 更新现有任务记录
        taskRecord.status = 'processing';
        if (!taskRecord.start_time) {
          taskRecord.start_time = new Date();
        }
        
        // 更新任务参数
        let existingParams = {};
        try {
          if (taskRecord.parameters) {
            existingParams = JSON.parse(taskRecord.parameters);
          }
        } catch (e) {
          logger.warn(`无法解析现有任务参数: ${e.message}`);
        }
        
        // 合并现有参数和新参数
        taskRecord.parameters = JSON.stringify({
          ...existingParams,
          ...taskParameters,
          updated: new Date().toISOString()
        });
        
        await taskRecord.save();
        logger.info(`已更新任务记录: ${params.taskId}`);
      }

      taskCreated = true;
      return taskRecord;
    } catch (error) {
      logger.error(`创建任务记录失败: ${params.taskId}`, error);
      return null;
    }
  };

  // 函数：设置任务超时
  const setupTaskTimeout = () => {
    if (!params.taskId) return null;

    const timeoutId = setTimeout(() => {
      logger.warn(`任务 ${params.taskId} 已超时 (${TASK_TIMEOUT}ms)，自动取消`);
      if (keyId) {
        apiKeyPoolManager.releaseApiService(keyId, false);
      }
      cancelTask(params.taskId);
    }, TASK_TIMEOUT);

    controller.timeoutId = timeoutId;
    return timeoutId;
  };

  // 函数：执行API请求
  const executeRequest = async () => {
    try {
      // 如果提供了serviceOverride参数，直接使用它而不是从池获取服务
      if (params.serviceOverride) {
        serviceInfo = params.serviceOverride;
        keyId = `override_${serviceInfo.id}_${Date.now()}`;
        logger.info(`使用指定的API服务进行测试: ${serviceInfo.serviceName}，请求任务: ${params.taskId || 'unknown'}`);
      } else {
        // 从API密钥池获取服务，增强版本支持健康检查和重试
        serviceInfo = await apiKeyPoolManager.getNextApiService();
        if (!serviceInfo) {
          throw new Error('无法获取有效的API服务');
        }
        keyId = serviceInfo.keyId;
        logger.info(`使用API服务: ${serviceInfo.serviceName}，请求任务: ${params.taskId || 'unknown'}`);
      }

      // 构建请求数据
      let data = {
        model: serviceInfo.modelName,
        messages: params.messages,
        ...params.options
      };

      // 检测并处理豆包API的参数冲突
      if (serviceInfo.serviceName.includes('doubao') || serviceInfo.serviceName.includes('豆包') || 
          serviceInfo.modelName.includes('doubao') || serviceInfo.modelName.includes('豆包')) {
        
        if (data.max_tokens !== undefined && data.max_completion_tokens !== undefined) {
          logger.warn(`executeRequest构建请求数据时检测到豆包API参数冲突：max_tokens和max_completion_tokens不能同时设置，已自动移除max_tokens参数`);
          delete data.max_tokens;
        }
      }

      // 如果有自定义请求格式，应用它
      data = applyRequestFormat(data, serviceInfo.requestFormat);

      // 发送请求
      try {
        const response = await axios.post(
          `${serviceInfo.baseUrl}/chat/completions`,
          data,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${serviceInfo.apiKey}`
            },
            signal: signal,
            timeout: 300000 // 5分钟超时
          }
        );

        // 记录API请求到系统日志
        if (params.taskId) {
          try {
            const { SystemLog } = require('../models');
            
            // 提取所有API参数
            const apiOptions = {};
            // 从data中提取所有参数，排除model和messages
            for (const key in data) {
              if (key !== 'model' && key !== 'messages') {
                apiOptions[key] = data[key];
              }
            }
            
            await SystemLog.create({
              user_id: params.userId || null,
              username: null,
              action: 'api_request',
              module: 'ai_service',
              description: `API请求 - 任务ID: ${params.taskId}`,
              ip_address: null,
              user_agent: null,
              status: 'success',
              details: JSON.stringify({
                taskId: params.taskId,
                model: data.model,
                messages: data.messages,
                serviceInfo: {
                  id: serviceInfo.id,
                  name: serviceInfo.serviceName,
                  modelName: serviceInfo.modelName
                },
                options: apiOptions
              }),
              source: 'backend',
              level: 'info'
            });
          } catch (logError) {
            logger.error(`记录API请求日志失败: ${logError.message}`);
          }
        }

        // 处理响应
        let result = response.data;

        // 记录API响应到系统日志
        if (params.taskId) {
          try {
            const { SystemLog } = require('../models');
            await SystemLog.create({
              user_id: params.userId || null,
              username: null,
              action: 'api_response',
              module: 'ai_service',
              description: `API响应 - 任务ID: ${params.taskId}`,
              ip_address: null,
              user_agent: null,
              status: 'success',
              details: JSON.stringify({
                taskId: params.taskId,
                response: result
              }),
              source: 'backend',
              level: 'info'
            });
          } catch (logError) {
            logger.error(`记录API响应日志失败: ${logError.message}`);
          }
        }

        // 使用 Object.assign 而不是 util._extend
        // 如果有自定义响应格式，应用它
        if (serviceInfo.responseFormat) {
          result = applyResponseFormat(result, serviceInfo.responseFormat);
        }

        // 释放API服务资源，标记为成功
        if (keyId) {
          apiKeyPoolManager.releaseApiService(keyId, true);
          keyId = null;
        }

        // 处理成功的任务
        if (params.taskId) {
          try {
            // 清除超时计时器
            if (controller.timeoutId) {
              clearTimeout(controller.timeoutId);
              controller.timeoutId = null;
            }

            // 从活跃任务中移除
            activeTasksMap.delete(params.taskId);

            // 使用Promise.resolve包装数据库操作以避免阻塞
            Promise.resolve().then(async () => {
              try {
                const taskRecord = await GenerationTask.findOne({
                  where: { task_id: params.taskId }
                });

                if (taskRecord) {
                  taskRecord.status = 'completed';
                  taskRecord.end_time = new Date();
                  if (taskRecord.start_time) {
                    taskRecord.duration_ms = new Date() - new Date(taskRecord.start_time);
                  }
                  // 记录使用的服务信息
                  const existingParams = JSON.parse(taskRecord.parameters || '{}');
                  taskRecord.parameters = JSON.stringify({
                    ...existingParams,
                    serviceId: serviceInfo.id,
                    serviceName: serviceInfo.serviceName,
                    retry: retryCount
                  });
                  await taskRecord.save();
                  logger.info(`任务完成并更新记录: ${params.taskId}`);
                }
              } catch (error) {
                logger.error(`更新任务完成状态失败: ${params.taskId}`, error);
              }
            });
          } catch (error) {
            logger.error(`处理任务完成状态失败: ${params.taskId}`, error);
          }
        }

        // 增加请求计数器
        consecutiveRequestCount++;

        // 检查是否需要主动触发垃圾回收
        tryOptimizeMemory();

        return {
          success: true,
          data: result,
          service: serviceInfo,
          ai_service: {
            name: serviceInfo.serviceName,
            model: serviceInfo.modelName
          }
        };
      } catch (axiosError) {
        // 增强错误日志记录，帮助诊断400错误
        if (axiosError.response) {
          // 服务器响应了错误状态码
          logger.error(`API请求失败(${axiosError.response.status}): ${params.taskId || 'unknown'}`, {
            statusCode: axiosError.response.status,
            statusText: axiosError.response.statusText,
            responseData: axiosError.response.data,
            url: `${serviceInfo.baseUrl}/chat/completions`,
            requestData: JSON.stringify(data)
          });
          
          // 记录详细错误到系统日志
          try {
            const { SystemLog } = require('../models');
            await SystemLog.create({
              user_id: params.userId || null,
              username: null,
              action: 'api_error',
              module: 'ai_service',
              description: `API错误(${axiosError.response.status}) - 任务ID: ${params.taskId || 'unknown'}`,
              ip_address: null,
              user_agent: null,
              status: 'error',
              details: JSON.stringify({
                error: {
                  statusCode: axiosError.response.status,
                  statusText: axiosError.response.statusText,
                  responseData: axiosError.response.data
                },
                request: {
                  url: `${serviceInfo.baseUrl}/chat/completions`,
                  data: data
                }
              }),
              source: 'backend',
              level: 'error'
            });
          } catch (logError) {
            logger.error(`记录API错误日志失败: ${logError.message}`);
          }
          
          // 转换为更明确的错误
          if (axiosError.response.status === 400) {
            throw new Error(`API参数错误(400): ${JSON.stringify(axiosError.response.data)}`);
          } else {
            throw new Error(`API请求失败(${axiosError.response.status}): ${axiosError.response.statusText}`);
          }
        }
        
        // 如果没有响应对象，继续抛出原始错误
        throw axiosError;
      }
    } catch (error) {
      // 检查错误类型
      const isTimeoutError = error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT' ||
                           error.message.includes('timeout') || error.message.includes('ETIMEDOUT');
      const isNetworkError = error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' ||
                           error.message.includes('network') || error.message.includes('Network Error');
      const isAbortError = error.name === 'AbortError' || error.name === 'CanceledError';

      // 释放API服务资源，标记为失败（除非是主动取消）
      if (keyId) {
        apiKeyPoolManager.releaseApiService(keyId, !(isTimeoutError || isNetworkError || (isAbortError && !signal.aborted)));
        keyId = null;
      }

      // 判断是否可以重试
      const canRetry = (isTimeoutError || isNetworkError) && retryCount < MAX_RETRIES && !signal.aborted && !params.serviceOverride;

      if (canRetry) {
        logger.warn(`请求失败，准备重试 (${retryCount+1}/${MAX_RETRIES}): ${error.message}`);
        retryCount++;

        // 重要：释放当前密钥，标记为不健康
        if (keyId) {
          apiKeyPoolManager.releaseApiService(keyId, false);
          keyId = null;
        }

        // 更新任务记录以反映重试
        if (params.taskId) {
          GenerationTask.findOne({ where: { task_id: params.taskId } })
            .then(taskRecord => {
              if (taskRecord) {
                taskRecord.parameters = JSON.stringify({
                  ...JSON.parse(taskRecord.parameters || '{}'),
                  retry: retryCount,
                  lastError: error.message
                });
                return taskRecord.save();
              }
            })
            .catch(err => logger.error(`更新重试状态失败: ${params.taskId}`, err));
        }

        // 延迟重试，避免立即失败
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 递归调用自身进行重试（但仅执行请求部分，不创建新任务）
        return await executeRequest();
      }

      // 无法重试或重试已用尽，更新失败状态
      if (params.taskId) {
        Promise.resolve().then(async () => {
          try {
            // 清除超时计时器
            if (controller.timeoutId) {
              clearTimeout(controller.timeoutId);
              controller.timeoutId = null;
            }

            // 从活跃任务中移除
            activeTasksMap.delete(params.taskId);

            const taskRecord = await GenerationTask.findOne({
              where: { task_id: params.taskId }
            });

            if (taskRecord) {
              // 如果是取消的任务
              if (isAbortError && signal.aborted) {
                taskRecord.status = 'canceled';
              } else {
                taskRecord.status = 'failed';
              }

              taskRecord.end_time = new Date();
              if (taskRecord.start_time) {
                taskRecord.duration_ms = new Date() - new Date(taskRecord.start_time);
              }

              taskRecord.error_message = error.message || '未知错误';

              // 更新参数以包含错误细节
              taskRecord.parameters = JSON.stringify({
                ...JSON.parse(taskRecord.parameters || '{}'),
                retryAttempts: retryCount,
                errorCode: error.code || 'UNKNOWN',
                errorType: isTimeoutError ? 'TIMEOUT' : (isNetworkError ? 'NETWORK' : (isAbortError ? 'ABORTED' : 'OTHER'))
              });

              await taskRecord.save();
              logger.error(`任务失败并更新记录: ${params.taskId} ${error.message}`);
            }
          } catch (updateError) {
            logger.error(`更新任务失败状态出错: ${params.taskId}`, updateError);
          }
        });
      }

      // 返回错误信息
      if (isAbortError && signal.aborted) {
        return {
          success: false,
          error: '任务已取消',
          cancelled: true
        };
      }

      return {
        success: false,
        error: error.message || '生成内容时出错',
        details: error.stack,
        retriable: isTimeoutError || isNetworkError,
        attemptsMade: retryCount
      };
    }
  };

  try {
    // 步骤1: 如果提供了任务ID，初始化任务
    if (params.taskId) {
      // 检查是否有同一用户的其他任务正在进行，如果有，先取消它们
      if (params.userId) {
        try {
          // 查找同一用户的其他处理中的任务
          const otherTasks = await GenerationTask.findAll({
            where: {
              user_id: params.userId,
              status: 'processing'
            }
          });

          if (otherTasks.length > 0) {
            logger.info(`用户 ${params.userId} 有 ${otherTasks.length} 个处理中任务，尝试取消`);

            // 遍历取消每个任务
            for (const task of otherTasks) {
              await cancelTask(task.task_id, `系统自动取消: 用户 ${params.userId} 开始新任务 ${params.taskId}`);
            }
          }
        } catch (error) {
          logger.error(`尝试取消用户 ${params.userId} 的其他任务失败:`, error);
        }
      }

      // 存储到活跃任务映射表
      activeTasksMap.set(params.taskId, controller);

      // 创建任务记录
      await createOrUpdateTask();

      // 设置任务超时
      setupTaskTimeout();
    }

    // 步骤2: 执行API请求
    return await executeRequest();

  } catch (error) {
    // 确保资源已正确释放
    if (keyId) {
      apiKeyPoolManager.releaseApiService(keyId, false);
      keyId = null;
    }

    // 清除超时计时器
    if (controller.timeoutId) {
      clearTimeout(controller.timeoutId);
      controller.timeoutId = null;
    }

    // 如果任务在活跃映射表中，移除它
    if (params.taskId && activeTasksMap.has(params.taskId)) {
      activeTasksMap.delete(params.taskId);
    }

    // 更新任务状态为失败
    if (params.taskId) {
      Promise.resolve().then(async () => {
        try {
          const taskRecord = await GenerationTask.findOne({
            where: { task_id: params.taskId }
          });

          if (taskRecord) {
            taskRecord.status = 'failed';
            taskRecord.end_time = new Date();
            if (taskRecord.start_time) {
              taskRecord.duration_ms = new Date() - new Date(taskRecord.start_time);
            }
            taskRecord.error_message = error.message || '生成内容时发生未处理的错误';
            await taskRecord.save();
            logger.error(`任务失败并更新记录（未处理错误）: ${params.taskId}`, error);
          }
        } catch (updateError) {
          logger.error(`更新任务失败状态时发生错误: ${params.taskId}`, updateError);
        }
      });
    }

    // 返回错误信息
    return {
      success: false,
      error: error.message || '生成内容时发生未处理的错误',
      details: error.stack,
      unhandledError: true
    };
  }
};

/**
 * 应用自定义请求格式
 * @param {Object} data - 原始请求数据
 * @param {Object|string} format - 自定义格式
 * @returns {Object} - 应用格式后的数据
 */
const applyRequestFormat = (data, format) => {
  try {
    // 如果format是字符串，尝试解析为JSON
    let formatObj = format;
    if (typeof format === 'string') {
      try {
        formatObj = JSON.parse(format);
      } catch (e) {
        logger.warn('无法解析请求格式字符串:', e);
        formatObj = {};
      }
    }

    // 创建一个基于格式的新对象
    const formattedData = { ...formatObj };

    // 处理bodyTemplate中的请求参数，这是实际发送给API的核心参数
    if (formatObj.bodyTemplate && typeof formatObj.bodyTemplate === 'object') {
      const bodyTemplate = formatObj.bodyTemplate;
      
      // 替换{{modelName}}模板变量
      if (bodyTemplate.model === '{{modelName}}' && data.model) {
        bodyTemplate.model = data.model;
      }
      
      // 替换{{messages}}模板变量
      if (bodyTemplate.messages === '{{messages}}' && data.messages) {
        bodyTemplate.messages = data.messages;
      }
      
      // 处理enable_search参数 - 通义千问API支持两种方式
      if (data.enable_search !== undefined && data.enable_search !== null) {
        // 方式1: 直接作为顶层参数
        bodyTemplate.enable_search = data.enable_search;
        
        // 方式2: 作为extra_body对象的属性
        if (!bodyTemplate.extra_body) {
          bodyTemplate.extra_body = {};
        }
        bodyTemplate.extra_body.enable_search = data.enable_search;
      }
      
      // 处理response_format参数 - 通义千问API要求这是一个对象而非字符串
      if (data.response_format !== undefined && data.response_format !== null) {
        if (typeof data.response_format === 'string') {
          // 如果是字符串，转换为对象格式
          bodyTemplate.response_format = { type: data.response_format };
        } else {
          // 如果已经是对象，直接使用
          bodyTemplate.response_format = data.response_format;
        }
      }
      
      // 将所有参数（除了已特殊处理的）从data复制到bodyTemplate
      for (const key in data) {
        if (key !== 'messages' && key !== 'model' && key !== 'enable_search' && 
            key !== 'response_format' && data[key] !== undefined && data[key] !== null) {
          bodyTemplate[key] = data[key];
        }
      }
      
      // 检测并处理豆包API的参数冲突
      if ((data.model && (data.model.includes('doubao') || data.model.includes('豆包'))) || 
          (formatObj.baseUrl && formatObj.baseUrl.includes('doubao'))) {
        // 检查是否同时存在max_tokens和max_completion_tokens
        if (bodyTemplate.max_tokens !== undefined && bodyTemplate.max_completion_tokens !== undefined) {
          logger.warn(`检测到豆包API参数冲突：max_tokens和max_completion_tokens不能同时设置，已自动移除max_tokens参数`);
          delete bodyTemplate.max_tokens;
        }
      }
      
      // 记录日志
      if (process.env.NODE_ENV === 'development') {
        logger.info(`bodyTemplate中的参数: ${JSON.stringify(bodyTemplate, null, 2)}`);
      }
      
      // 重要：当存在bodyTemplate时，返回处理后的bodyTemplate
      return bodyTemplate;
    }

    // 如果没有bodyTemplate，处理其他情况（保持现有行为）
    
    // 处理模板变量
    if (formatObj.messages && Array.isArray(formatObj.messages)) {
      formattedData.messages = formatObj.messages.map(msg => {
        if (msg.content && typeof msg.content === 'string' && msg.content.includes('${prompt}')) {
          // 替换${prompt}变量
          const promptContent = data.messages && data.messages.length > 0 ?
            data.messages[data.messages.length - 1].content : '';
          return {
            ...msg,
            content: msg.content.replace('${prompt}', promptContent)
          };
        }
        return msg;
      });
    } else {
      // 确保消息和模型信息被保留
      if (data.messages) {
        if (!formattedData.messages) {
          formattedData.messages = data.messages;
        } else if (formattedData.prompt) {
          // 某些API使用prompt而不是messages
          formattedData.prompt = data.messages.map(m => m.content).join('\n');
        }
      }
    }

    // 确保模型名称被保留
    if (data.model && !formattedData.model) {
      formattedData.model = data.model;
    }

    // 处理enable_search参数 - 非bodyTemplate模式也支持两种方式
    if (data.enable_search !== undefined && data.enable_search !== null) {
      // 方式1: 直接作为顶层参数
      formattedData.enable_search = data.enable_search;
      
      // 方式2: 作为extra_body对象的属性
      if (!formattedData.extra_body) {
        formattedData.extra_body = {};
      }
      formattedData.extra_body.enable_search = data.enable_search;
    }

    // 处理response_format参数 - 通义千问API要求这是一个对象而非字符串
    if (data.response_format !== undefined && data.response_format !== null) {
      if (typeof data.response_format === 'string') {
        // 如果是字符串，转换为对象格式
        formattedData.response_format = { type: data.response_format };
      } else {
        // 如果已经是对象，直接使用
        formattedData.response_format = data.response_format;
      }
    }

    // 将所有参数（除了已特殊处理的）从data复制到formattedData
    for (const key in data) {
      if (key !== 'messages' && key !== 'model' && key !== 'enable_search' && 
          key !== 'response_format' && data[key] !== undefined && data[key] !== null) {
        formattedData[key] = data[key];
      }
    }
    
    // 检测并处理豆包API的参数冲突
    if ((data.model && (data.model.includes('doubao') || data.model.includes('豆包'))) ||
        (formattedData.model && (formattedData.model.includes('doubao') || formattedData.model.includes('豆包')))) {
      // 检查是否同时存在max_tokens和max_completion_tokens
      if (formattedData.max_tokens !== undefined && formattedData.max_completion_tokens !== undefined) {
        logger.warn(`检测到豆包API参数冲突：max_tokens和max_completion_tokens不能同时设置，已自动移除max_tokens参数`);
        delete formattedData.max_tokens;
      }
    }

    // 添加日志记录，确保我们可以看到实际应用的值
    if (process.env.NODE_ENV === 'development') {
      logger.info(`请求格式化后的参数: ${JSON.stringify(formattedData, null, 2)}`);
    }

    return formattedData;
  } catch (error) {
    logger.error('应用请求格式失败:', error);
    return data; // 出错时返回原始数据
  }
};

/**
 * 应用自定义响应格式
 * @param {Object} result - 原始响应数据
 * @param {Object|string} format - 自定义格式
 * @returns {Object} - 应用格式后的数据
 */
const applyResponseFormat = (result, format) => {
  try {
    // 如果format是字符串，尝试解析为JSON
    let formatObj = format;
    if (typeof format === 'string') {
      try {
        formatObj = JSON.parse(format);
      } catch (e) {
        logger.warn('无法解析响应格式字符串:', e);
        formatObj = {};
      }
    }

    // 检查是否已经是标准格式
    if (result.choices && result.choices.length > 0 && result.choices[0].message) {
      return result;
    }

    // 如果是DeepSeek响应格式，确保它符合我们的标准格式
    if (result.choices && result.choices.length > 0) {
      // 如果没有message字段，添加它
      if (!result.choices[0].message && result.choices[0].text) {
        result.choices[0].message = {
          content: result.choices[0].text,
          role: 'assistant'
        };
      }
      return result;
    }

    // 否则尝试将结果转换为预期格式
    let formattedResult = { ...formatObj };

    // 处理path字段，用于从嵌套对象中提取数据
    if (formatObj.path && typeof formatObj.path === 'string') {
      try {
        // 将path分解为属性数组，例如"data.message.content" => ["data", "message", "content"]
        const pathParts = formatObj.path.split('.');
        let value = result;

        // 遍历路径部分，从嵌套对象中提取数据
        for (const part of pathParts) {
          if (value && typeof value === 'object' && part in value) {
            value = value[part];
          } else {
            value = null;
            break;
          }
        }

        if (value !== null && value !== undefined) {
          // 将提取的内容包装为标准格式
          formattedResult = {
            choices: [{
              message: {
                content: typeof value === 'string' ? value : JSON.stringify(value),
                role: 'assistant'
              },
              finish_reason: 'stop'
            }]
          };
          return formattedResult;
        }
      } catch (e) {
        logger.warn('使用path提取数据失败:', e);
      }
    }

    // 确保响应内容被正确映射
    if (result.response || result.output || result.generated_text) {
      formattedResult.choices = [{
        message: {
          content: result.response || result.output || result.generated_text,
          role: 'assistant'
        },
        finish_reason: 'stop'
      }];
    } else if (typeof result === 'string') {
      // 如果结果是纯文本，将其包装为标准格式
      formattedResult = {
        choices: [{
          message: {
            content: result,
            role: 'assistant'
          },
          finish_reason: 'stop'
        }]
      };
    }

    return formattedResult;
  } catch (error) {
    logger.error('应用响应格式失败:', error);
    return result; // 出错时返回原始结果
  }
};

/**
 * 从AI响应中提取HTML内容
 */
const extractHtmlFromResponse = (response) => {
  try {
    // 获取AI回复的内容
    let content = '';

    if (response.choices && response.choices.length > 0) {
      // 处理不同的响应格式
      if (response.choices[0].message && response.choices[0].message.content) {
        content = response.choices[0].message.content;
      } else if (response.choices[0].text) {
        content = response.choices[0].text;
      }
    } else if (typeof response === 'string') {
      // 如果响应是纯文本
      content = response;
    }

    // 如果内容为空，返回错误提示
    if (!content) {
      logger.warn('响应中没有可用的内容');
      return `<div>生成内容为空</div>`;
    }

    // 如果内容是JSON字符串，尝试解析
    if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
      try {
        const jsonContent = JSON.parse(content);
        if (jsonContent.html) {
          content = jsonContent.html;
        }
      } catch (e) {
        // 如果不是有效的JSON，保持原样
      }
    }

    // 提取HTML代码
    const htmlRegex = /<html[\s\S]*?<\/html>/i;
    const bodyRegex = /<body[\s\S]*?<\/body>/i;

    let html = '';

    // 尝试提取完整的HTML
    const htmlMatch = content.match(htmlRegex);
    if (htmlMatch) {
      html = htmlMatch[0];
    } else {
      // 尝试提取body部分
      const bodyMatch = content.match(bodyRegex);
      if (bodyMatch) {
        html = bodyMatch[0];
      } else {
        // 尝试提取任何HTML标签块
        const tagRegex = /<div[\s\S]*?<\/div>|<section[\s\S]*?<\/section>/i;
        const tagMatch = content.match(tagRegex);

        if (tagMatch) {
          html = tagMatch[0];
        } else {
          // 如果内容包含```html```代码块
          const codeBlockRegex = /```html([\s\S]*?)```/;
          const codeBlockMatch = content.match(codeBlockRegex);

          if (codeBlockRegex && codeBlockMatch[1]) {
            html = codeBlockMatch[1].trim();
          } else {
            // 如果没有找到任何HTML标签，将内容包装在div中
            html = `<div>${content}</div>`;
          }
        }
      }
    }

    // 清理HTML，确保安全
    html = cleanHtml(html);

    return html;
  } catch (error) {
    logger.error('提取HTML内容失败:', error);
    return `<div>生成内容解析失败</div>`;
  }
};

/**
 * 清理HTML，移除不安全的标签和属性
 * @param {String} html - 原始HTML
 * @returns {String} - 清理后的HTML
 */
const cleanHtml = (html) => {
  // 移除script标签
  html = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

  // 移除onclick等事件属性
  html = html.replace(/\son\w+\s*=\s*["'][^"']*["']/gi, '');

  // 移除iframe
  html = html.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '');

  // 移除外部资源引用
  html = html.replace(/<link\b[^>]*>/gi, '');

  // 确保文本内容不会被截断
  html = html.replace(/<div/gi, '<div style="overflow-wrap: break-word; word-break: break-all;"');

  return html;
};

const sendRequest = async (requestData) => {
  try {
    // 获取当前激活的AI服务
    const service = await getActiveAIService();

    // 构建请求数据
    let data = {
      model: service.modelName,
      messages: requestData.messages,
      ...requestData.options
    };

    // 如果有自定义请求格式，应用它
    data = applyRequestFormat(data, service.requestFormat);

    // 发送请求
    const response = await axios.post(
      `${service.baseUrl}/chat/completions`,
      data,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${service.apiKey}`
        },
        timeout: 300000 // 5分钟超时
      }
    );

    // 处理响应
    let result = response.data;

    // 如果有自定义响应格式，应用它
    if (service.responseFormat) {
      result = applyResponseFormat(result, service.responseFormat);
    }

    return {
      success: true,
      data: result,
      ai_service: {
        name: service.serviceName,
        model: service.modelName
      }
    };
  } catch (error) {
    logger.error('发送请求失败:', error);
    return {
      success: false,
      error: error.message
    };

    // 无论成功或失败，确保清理任务资源
    if (params?.taskId && activeTasksMap.has(params.taskId)) {
      logger.info(`请求完成后清理任务资源: ${params.taskId}`);
      activeTasksMap.delete(params.taskId);
      taskCreationTime.delete(params.taskId);
    }

    // 增加请求计数器
    consecutiveRequestCount++;

    // 检查是否需要主动触发垃圾回收
    tryOptimizeMemory();
  }
};

/**
 * 初始化密钥池
 */
// 定期清理长时间未完成的任务
const startTaskCleanupScheduler = () => {
  setInterval(async () => {
    try {
      const now = Date.now();
      const taskEntries = Array.from(taskCreationTime.entries());
      let cleanedTasks = 0;

      for (const [taskId, creationTime] of taskEntries) {
        // 检查任务是否超时
        if (now - creationTime > MAX_TASK_LIFETIME) {
          logger.warn(`清理长时间未完成的任务: ${taskId}, 已运行 ${Math.round((now - creationTime)/1000)} 秒`);

          // 从映射表中移除
          activeTasksMap.delete(taskId);
          taskCreationTime.delete(taskId);
          cleanedTasks++;

          // 更新数据库中的任务状态
          try {
            const task = await GenerationTask.findOne({
              where: { task_id: taskId }
            });

            if (task && task.status === 'processing') {
              task.status = 'failed';
              task.error_message = '任务超时自动清理';
              task.completed_at = new Date();
              await task.save();
              logger.info(`已更新数据库中任务 ${taskId} 的状态为 failed`);
            }
          } catch (dbError) {
            logger.error(`更新超时任务 ${taskId} 状态失败:`, dbError);
          }
        }
      }

      if (cleanedTasks > 0) {
        logger.info(`定时清理已完成, 清理了 ${cleanedTasks} 个超时任务`);
        logger.info(`当前活跃任务数: ${activeTasksMap.size}, 任务创建时间记录数: ${taskCreationTime.size}`);
      }
    } catch (error) {
      logger.error('定时清理任务出错:', error);
    }
  }, CLEANUP_INTERVAL);
};

const initKeyPool = async () => {
  try {
    await apiKeyPoolManager.refreshKeyPool();

    // 检查是否有活跃的服务，如果没有则激活第一个
    if (apiKeyPoolManager.getKeyPool().length === 0) {
      const firstService = await AIServiceConfig.findOne({
        order: [['id', 'ASC']]
      });

      if (firstService) {
        firstService.is_active = true;
        await firstService.save();
        logger.info(`没有活跃的API服务，已自动激活第一个可用服务: ${firstService.service_name}`);

        // 重新刷新密钥池以加载刚刚激活的服务
        await apiKeyPoolManager.refreshKeyPool();
      }
    }

    isInitialized = true;
    logger.info('API密钥池初始化完成');

    // 启动定期清理任务的调度器
    startTaskCleanupScheduler();
    logger.info('已启动任务自动清理调度器');
  } catch (error) {
    logger.error('API密钥池初始化失败:', error);
  }
};

/**
 * 尝试优化内存使用
 * 使用轻量级的方式帮助释放内存资源
 */
const tryOptimizeMemory = () => {
  try {
    // 检查是否超过连续请求阈值
    if (consecutiveRequestCount >= GC_TRIGGER_THRESHOLD) {
      // 获取当前内存使用情况
      const memoryUsage = process.memoryUsage();
      const heapUsed = memoryUsage.heapUsed;

      logger.info(`当前内存使用: ${Math.round(heapUsed / 1024 / 1024)}MB，连续请求次数: ${consecutiveRequestCount}`);

      // 如果超过内存阈值，尝试帮助回收内存
      if (heapUsed > MEMORY_THRESHOLD) {
        logger.warn('内存使用量超过阈值，清理临时对象...');

        // 使用安全的轻量级方法帮助垃圾回收
        try {
          // 清理临时对象引用
          global._activeTasksCleanup = [...activeTasksMap.keys()];
          for (const oldTask of global._activeTasksCleanup) {
            if (Date.now() - (taskCreationTime.get(oldTask) || 0) > 120000) { // 2分钟前的任务
              logger.info(`清理长时间未使用的任务引用: ${oldTask}`);
              activeTasksMap.delete(oldTask);
              taskCreationTime.delete(oldTask);
            }
          }
          global._activeTasksCleanup = null;

          // 手动清理临时变量
          for (let i = 0; i < 20; i++) {
            const tmp = { };
            for (let j = 0; j < 1000; j++) {
              tmp[`key_${j}`] = null;
            }
          }

          // 记录清理尝试
          logger.info('内存优化操作完成');
        } catch (gcError) {
          logger.error('清理临时对象过程中出错:', gcError);
        }
      }

      // 重置请求计数器
      consecutiveRequestCount = 0;
    }
  } catch (error) {
    logger.error('内存优化失败:', error);
    // 重置计数器，防止卡在错误状态
    consecutiveRequestCount = 0;
  }
};

module.exports = {
  sendRequest,
  getActiveAIService,
  generateContent,
  extractHtmlFromResponse,
  cancelTask,
  getActiveTasksCount,
  getApiKeyPoolStatus: apiKeyPoolManager.getKeyPoolStatus,
  refreshApiKeyPool: apiKeyPoolManager.refreshKeyPool,
  applyRequestFormat,
  applyResponseFormat,
  initKeyPool,
  tryOptimizeMemory // 导出内存优化函数，便于其他模块使用
};
