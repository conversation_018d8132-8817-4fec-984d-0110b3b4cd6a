// styles.js - 风格管理模块

// 风格管理模块命名空间
window.styleModule = (function() {
  // 初始化函数
  function init() {
    console.log('初始化风格管理模块...');

    // 绑定事件
    bindEvents();

    // 加载风格列表
    fetchStyles();
  }

  // 绑定事件处理函数
  function bindEvents() {
    // 添加风格按钮
    document.getElementById('addStyleBtn').addEventListener('click', function() {
      // 重置表单
      document.getElementById('styleForm').reset();
      document.getElementById('styleId').value = '';

      // 设置模态框标题
      document.getElementById('styleModalLabel').textContent = '添加风格';

      // 启用id_code输入框，允许输入新的标识码
      document.getElementById('styleIdCode').disabled = false;

      // 隐藏预览图容器
      document.getElementById('previewImageContainer').classList.add('d-none');
      document.getElementById('previewImagePreview').src = '';

      // 显示模态框
      const modal = new bootstrap.Modal(document.getElementById('styleModal'));
      modal.show();

      // 清除表单的数据标记
      document.getElementById('styleForm').removeAttribute('data-style-id');
    });

    // 图片预览和上传相关代码
    document.getElementById('stylePreviewImage').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          document.getElementById('previewImagePreview').src = e.target.result;
          document.getElementById('previewImageContainer').classList.remove('d-none');
        };
        reader.readAsDataURL(file);
      }
    });

    document.getElementById('removePreviewImageBtn').addEventListener('click', function() {
      document.getElementById('stylePreviewImage').value = '';
      document.getElementById('previewImageContainer').classList.add('d-none');
      document.getElementById('previewImagePreview').src = '';
    });

    // 保存风格按钮
    document.getElementById('saveStyleBtn').addEventListener('click', function() {
      saveStyle();
    });
  }

  // 获取风格列表
  function fetchStyles() {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch('/api/admin/style-prompts', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // 处理后端返回的数据结构
        // 检查data.data.stylePrompts是否存在
        const styles = data.data && data.data.stylePrompts ? data.data.stylePrompts :
                      (Array.isArray(data.data) ? data.data : []);
        console.log('处理后的风格数据:', styles);
        renderStyles(styles);
      } else {
        console.error('获取风格列表失败:', data.message);
        renderStyles([]);
      }
    })
    .catch(error => {
      console.error('获取风格列表失败:', error);
      renderStyles([]);
    });
  }

  // 渲染风格列表
  function renderStyles(styles) {
    const tbody = document.querySelector('#stylesTable tbody');
    tbody.innerHTML = '';

    if (styles.length === 0) {
      const tr = document.createElement('tr');
      tr.innerHTML = '<td colspan="6" class="text-center">暂无数据</td>';
      tbody.appendChild(tr);
      return;
    }

    styles.forEach(style => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td>${style.id}</td>
        <td>${style.id_code || '无标识码'}</td>
        <td>${style.style_name}</td>
        <td>${style.prompt_content.substring(0, 50)}${style.prompt_content.length > 50 ? '...' : ''}</td>
        <td>${style.display_order || 0}</td>
        <td>
          <span class="badge ${style.status === 'active' ? 'bg-success' : 'bg-secondary'}">
            ${style.status === 'active' ? '启用' : '禁用'}
          </span>
        </td>
        <td>
          <button class="btn btn-sm btn-primary edit-style" data-id="${style.id}">编辑</button>
          <button class="btn btn-sm btn-danger delete-style" data-id="${style.id}" data-name="${style.style_name}">删除</button>
        </td>
      `;

      // 绑定编辑按钮事件
      tr.querySelector('.edit-style').addEventListener('click', function() {
        editStyle(style.id);
      });

      // 绑定删除按钮事件
      tr.querySelector('.delete-style').addEventListener('click', function() {
        confirmDeleteStyle(style.id, style.style_name);
      });

      tbody.appendChild(tr);
    });
  }

  // 编辑风格
  function editStyle(styleId) {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch(`/api/admin/style-prompts/${styleId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // 处理后端返回的数据结构
        const style = data.data && data.data.stylePrompt ? data.data.stylePrompt : data.data;

        if (!style) {
          alert('获取风格详情失败: 数据格式错误');
          return;
        }

        // 设置模态框标题
        document.getElementById('styleModalLabel').textContent = '编辑风格';

        // 填充表单
        document.getElementById('styleId').value = style.id;
        document.getElementById('styleIdCode').value = style.id_code || '';
        document.getElementById('styleName').value = style.style_name;
        document.getElementById('stylePromptContent').value = style.prompt_content;
        document.getElementById('displayOrder').value = style.display_order || 0;
        document.getElementById('styleStatus').checked = style.status === 'active';
        // 加载示例HTML
        document.getElementById('exampleHtml').value = style.example_html || '';

        // 如果是编辑模式，禁用id_code输入框，避免修改唯一标识码
        document.getElementById('styleIdCode').disabled = true;

        // 如果有示例图，显示预览
        if (style.StyleExamples && style.StyleExamples.length > 0 && style.StyleExamples[0].image_url) {
          const imageUrl = style.StyleExamples[0].image_url;
          document.getElementById('previewImagePreview').src = imageUrl;
          document.getElementById('previewImageContainer').classList.remove('d-none');
        } else {
          document.getElementById('previewImageContainer').classList.add('d-none');
          document.getElementById('previewImagePreview').src = '';
        }

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('styleModal'));
        modal.show();

        // 保存风格ID到表单上
        document.getElementById('styleForm').setAttribute('data-style-id', style.id);
      } else {
        alert('获取风格详情失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('获取风格详情失败:', error);
      alert('获取风格详情失败，请稍后再试');
    });
  }

  // 确认删除风格
  function confirmDeleteStyle(styleId, styleName) {
    // 填充确认对话框
    document.getElementById('deleteStyleId').value = styleId;
    document.getElementById('deleteStyleName').textContent = styleName;

    // 显示确认对话框
    const modal = new bootstrap.Modal(document.getElementById('deleteStyleModal'));
    modal.show();

    // 绑定确认删除按钮事件
    document.getElementById('confirmDeleteStyleBtn').onclick = function() {
      deleteStyle(styleId);
    };
  }

  // 删除风格
  function deleteStyle(styleId) {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch(`/api/admin/style-prompts/${styleId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      // 关闭确认对话框
      bootstrap.Modal.getInstance(document.getElementById('deleteStyleModal')).hide();

      if (data.success) {
        // 刷新风格列表
        fetchStyles();
        alert('删除风格成功');
      } else {
        alert('删除风格失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('删除风格失败:', error);

      // 关闭确认对话框
      bootstrap.Modal.getInstance(document.getElementById('deleteStyleModal')).hide();

      alert('删除风格失败，请稍后再试');
    });
  }

  // 保存风格
  function saveStyle() {
    // 验证表单
    const form = document.getElementById('styleForm');
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }

    const styleId = document.getElementById('styleId').value;
    const styleIdCode = document.getElementById('styleIdCode').value;
    const styleName = document.getElementById('styleName').value;
    const promptContent = document.getElementById('stylePromptContent').value;
    const displayOrder = document.getElementById('displayOrder').value;
    const status = document.getElementById('styleStatus').checked ? 'active' : 'inactive';
    const imageFile = document.getElementById('stylePreviewImage').files[0];
    // 获取示例HTML
    const exampleHtml = document.getElementById('exampleHtml').value;

    if (!styleIdCode || !styleName || !promptContent) {
      alert('请填写必填字段');
      return;
    }

    // 验证风格标识码格式
    const idCodeRegex = /^[a-zA-Z0-9_]+$/;
    if (!idCodeRegex.test(styleIdCode)) {
      alert('风格标识码只能包含字母、数字和下划线');
      return;
    }

    // 构建 FormData 对象，用于文件上传
    const formData = new FormData();
    formData.append('id_code', styleIdCode);
    formData.append('style_name', styleName);
    formData.append('prompt_content', promptContent);
    formData.append('display_order', displayOrder || 0);
    formData.append('status', status);
    // 添加示例HTML
    formData.append('example_html', exampleHtml || '');

    // 添加图片文件（如果有）
    if (imageFile) {
      formData.append('preview_image', imageFile);
    }

    // 根据是否有ID决定是新增还是更新
    const url = styleId
      ? `/api/admin/style-prompts/${styleId}`
      : '/api/admin/style-prompts';

    const method = styleId ? 'PUT' : 'POST';
    const token = localStorage.getItem('token');

    if (!token) return;

    // 显示加载状态
    const saveBtn = document.getElementById('saveStyleBtn');
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';

    fetch(url, {
      method,
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData // 使用 FormData 而不是 JSON
    })
    .then(response => response.json())
    .then(data => {
      // 恢复按钮状态
      saveBtn.disabled = false;
      saveBtn.innerHTML = '保存';

      if (data.success) {
        // 关闭模态框
        bootstrap.Modal.getInstance(document.getElementById('styleModal')).hide();

        // 刷新风格列表
        fetchStyles();

        // 显示成功消息
        alert(styleId ? '更新风格成功' : '添加风格成功');
      } else {
        alert(data.message || '操作失败');
      }
    })
    .catch(error => {
      console.error('保存风格失败:', error);

      // 恢复按钮状态
      saveBtn.disabled = false;
      saveBtn.innerHTML = '保存';

      alert('保存风格失败，请稍后再试');
    });
  }

  // 公开API
  return {
    init: init
  };
})();
