const logger = require('./logger');
const fs = require('fs');
const path = require('path');
const util = require('util'); // 引入 util 模块

/**
 * 提示词构建工具
 * 负责组合基础提示词、风格提示词和用户输入内容，生成完整的提示词
 */

/**
 * 构建完整的提示词
 * @param {Object} basePrompt - 基础提示词模板
 * @param {Object} stylePrompt - 风格提示词模板
 * @param {Object} userInput - 用户输入的内容
 * @returns {string} - 组合后的完整提示词
 */
const buildPrompt = (basePrompt, stylePrompt, userInput) => {
  try {
    // !!! DEBUG LOGGING START !!!
    logger.debug('[promptBuilder.buildPrompt] Received basePrompt (first 100 chars):', basePrompt && basePrompt.prompt_content ? basePrompt.prompt_content.substring(0, 100) : 'null or empty');
    logger.debug('[promptBuilder.buildPrompt] Received stylePrompt (first 100 chars):', stylePrompt && stylePrompt.prompt_content ? stylePrompt.prompt_content.substring(0, 100) : 'null or empty');
    logger.debug('[promptBuilder.buildPrompt] Received userInput:', util.inspect(userInput, { depth: null, colors: false }));
    // !!! DEBUG LOGGING END !!!

    // 提取提示词内容
    const basePromptContent = basePrompt.prompt_content || '';
    const stylePromptContent = stylePrompt.prompt_content || '';

    // 准备用户输入内容
    const {
      cover_text = '',
      account_name = '',
      subtitle = '',
      auto_title = false,
      // 添加自定义参数
      custom_size_enabled = false,
      custom_width = 900,
      custom_height = 383,
      custom_style_description = '',
      custom_style_enhanced = false,
      custom_image_url = '',
      custom_image_type = 'background' // 默认为背景图
    } = userInput;

    // !!! DEBUG LOGGING START !!!
    logger.debug('[promptBuilder.buildPrompt] Destructured userInput - cover_text:', cover_text, ', account_name:', account_name, ', subtitle:', subtitle, ', style_id:', userInput.styleId);
    // !!! DEBUG LOGGING END !!!

    // 组合用户输入内容
    let userContent = `内容：${cover_text}`;

    if (account_name) {
      userContent += `\n账号名称：${account_name}`;
    }

    if (subtitle) {
      userContent += `\n副标题：${subtitle}`;
    }

    // 如果启用自动标题功能，添加相应的提示
    if (auto_title) {
      userContent += `\n（提炼文案内容作为标题，不超过10个字）`;
    }

    // 处理自定义尺寸
    if (custom_size_enabled && custom_width && custom_height) {
      userContent += `\n自定义尺寸：宽度${custom_width}px，高度${custom_height}px`;
    }

    // 处理自定义图片
    if (custom_image_url) {
      if (custom_image_type === 'background') {
        userContent += `\n背景图片：${custom_image_url}（请将此图片作为封面的背景）`;
      } else if (custom_image_type === 'illustration') {
        userContent += `\n插图：${custom_image_url}（请将此图片作为封面的插图，放置在适当位置）`;
      }
    }

    // 处理自定义风格
    let finalStylePromptContent = stylePromptContent;
    if (custom_style_description) {
      finalStylePromptContent = `## 设计风格\n\n${custom_style_description}\n\n`;

      if (custom_style_enhanced) {
        finalStylePromptContent += `同时确保设计专业、美观，保持清晰可读，避免过于复杂的设计，确保文字与背景有足够的对比度。\n\n`;
      }
    }

    // 按照规定顺序组合提示词：基础提示词 + 风格提示词 + 用户输入内容
    const fullPrompt = [
      basePromptContent,
      finalStylePromptContent,
      userContent
    ].join('\n\n').trim();

    logger.debug('生成提示词成功');

    return fullPrompt;
  } catch (error) {
    logger.error('提示词构建失败:', error);
    throw new Error('提示词构建失败');
  }
};

/**
 * 清理AI生成的HTML内容
 * @param {string} html - 原始HTML内容
 * @returns {string} - 清理后的HTML内容
 */
const cleanHtml = (html) => {
  try {
    if (!html) return '';

    // 移除潜在的脚本标签
    let cleanedHtml = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

    // 处理常见的HTML实体
    cleanedHtml = cleanedHtml
      .replace(/&nbsp;/g, ' ')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");

    // 确保内容完整显示 - 特别重要，保证前端预览区域正常工作
    cleanedHtml = cleanedHtml
      .replace(/<p>/g, '<p style="white-space: pre-wrap; word-break: break-word; overflow: visible;">')
      .replace(/<div>/g, '<div style="white-space: pre-wrap; word-break: break-word; overflow: visible;">');

    return cleanedHtml;
  } catch (error) {
    logger.error('HTML清理失败:', error);
    return html || ''; // 如果清理失败，返回原始HTML或空字符串
  }
};

/**
 * 从API响应中提取HTML内容
 * @param {string} response - API响应内容
 * @returns {string} - 提取的HTML内容
 */
const extractHtmlFromResponse = (response) => {
  try {
    if (!response) return '';

    // 尝试将响应解析为JSON
    let jsonResponse;
    try {
      jsonResponse = JSON.parse(response);
    } catch (e) {
      // 如果不是JSON，假设是直接的HTML内容
      return cleanHtml(response);
    }

    // 如果是JSON，尝试从中提取HTML内容
    const html = jsonResponse.html || jsonResponse.content || jsonResponse.data?.html || '';

    return cleanHtml(html);
  } catch (error) {
    logger.error('HTML提取失败:', error);
    return '';
  }
};

module.exports = {
  buildPrompt,
  cleanHtml,
  extractHtmlFromResponse
};
