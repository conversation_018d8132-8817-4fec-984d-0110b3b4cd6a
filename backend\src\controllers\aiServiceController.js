const { AIServiceConfig } = require('../models');
const { successResponse, errorResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');
const { encryptApi<PERSON>ey, decrypt<PERSON>pi<PERSON>ey } = require('../utils/encryption');
const axios = require('axios');
const { getApiKeyPoolStatus, refreshApiKeyPool, applyRequestFormat, applyResponseFormat, generateContent } = require('../utils/aiServiceManager');
const { sequelize } = require('../models');

/**
 * 获取所有AI服务配置
 * @route GET /api/admin/ai-services
 */
const getAllAIServices = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      service = '',
      priority = '',
      weight = '',
      is_active = '',
      service_type = ''
    } = req.query;

    // 构建查询条件
    const whereClause = {};

    // 搜索条件：服务商、服务名、模型名
    if (search) {
      whereClause[sequelize.Op.or] = [
        { service: { [sequelize.Op.like]: `%${search}%` } },
        { service_name: { [sequelize.Op.like]: `%${search}%` } },
        { model_name: { [sequelize.Op.like]: `%${search}%` } }
      ];
    }

    // 按服务商筛选
    if (service) {
      whereClause.service = service;
    }

    // 按优先级筛选
    if (priority) {
      whereClause.priority = parseInt(priority);
    }

    // 按权重筛选
    if (weight) {
      whereClause.weight = parseInt(weight);
    }

    // 按激活状态筛选
    if (is_active !== '') {
      whereClause.is_active = is_active === '1' || is_active === 'true';
    }

    // 按服务类型筛选
    if (service_type) {
      whereClause.service_type = service_type;
    }

    // 计算分页参数
    const offset = (page - 1) * limit;

    // 查询总数
    const count = await AIServiceConfig.count({ where: whereClause });

    // 查询分页数据
    const services = await AIServiceConfig.findAll({
      where: whereClause,
      order: [
        ['priority', 'DESC'],
        ['created_at', 'DESC']
      ],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // 转换为前端需要的格式
    const formattedServices = services.map(service => ({
      id: service.id,
      service: service.service,
      service_name: service.service_name,
      base_url: service.base_url,
      api_key: service.api_key, // 在前端会被掩码处理
      model_name: service.model_name,
      is_active: service.is_active,
      weight: service.weight,
      priority: service.priority,
      max_concurrent_requests: service.max_concurrent_requests,
      service_type: service.service_type,
      created_at: service.created_at,
      updated_at: service.updated_at
    }));

    return successResponse(res, '获取AI服务配置列表成功', {
      services: formattedServices,
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(count / limit)
    });
  } catch (error) {
    logger.error('获取AI服务配置列表失败:', error);
    return errorResponse(res, '获取AI服务配置列表失败', 500);
  }
};

/**
 * 获取单个AI服务配置
 * @route GET /api/admin/ai-services/:id
 */
const getAIService = async (req, res) => {
  const { id } = req.params;

  try {
    const service = await AIServiceConfig.findByPk(id);

    if (!service) {
      return errorResponse(res, 'AI服务配置不存在', 404);
    }

    return successResponse(res, '获取AI服务配置成功', {
      service: {
        id: service.id,
        service: service.service || '',
        service_name: service.service_name,
        base_url: service.base_url,
        model_name: service.model_name,
        is_active: service.is_active,
        request_format: service.request_format || null,
        response_format: service.response_format || null,
        weight: service.weight || 1,
        priority: service.priority || 1,
        max_concurrent_requests: service.max_concurrent_requests || 0,
        service_type: service.service_type || '文本AI',
        created_at: service.created_at,
        updated_at: service.updated_at
      }
    });
  } catch (error) {
    logger.error('获取AI服务配置失败:', error);
    return errorResponse(res, '获取AI服务配置失败', 500);
  }
};

/**
 * 创建AI服务配置
 * @route POST /api/admin/ai-services
 */
const createAIService = async (req, res) => {
  const {
    service,
    service_name,
    base_url,
    api_key,
    model_name,
    request_format,
    response_format,
    weight,
    priority,
    max_concurrent_requests,
    service_type
  } = req.body;

  // 验证必填字段
  if (!service_name || !base_url || !api_key || !model_name) {
    return errorResponse(res, '服务名称、基础URL、API密钥和模型名称不能为空', 400);
  }

  try {
    // 加密API密钥
    const encryptedKey = encryptApiKey(api_key);

    // 创建新服务
    const newService = await AIServiceConfig.create({
      service: service || null,
      service_name,
      base_url,
      api_key: JSON.stringify(encryptedKey),
      model_name,
      request_format: request_format || null,
      response_format: response_format || null,
      weight: parseInt(weight) || 1,
      priority: parseInt(priority) || 1,
      max_concurrent_requests: parseInt(max_concurrent_requests) || 0,
      service_type: service_type || '文本AI',
      is_active: false
    });

    return successResponse(res, 'AI服务配置创建成功', {
      service: {
        id: newService.id,
        service: newService.service,
        service_name: newService.service_name,
        base_url: newService.base_url,
        model_name: newService.model_name,
        weight: newService.weight,
        priority: newService.priority,
        max_concurrent_requests: newService.max_concurrent_requests,
        service_type: newService.service_type,
        is_active: newService.is_active,
        created_at: newService.created_at
      }
    }, 201);
  } catch (error) {
    logger.error('创建AI服务配置失败:', error);
    return errorResponse(res, '创建AI服务配置失败', 500);
  }
};

/**
 * 更新AI服务配置
 * @route PUT /api/admin/ai-services/:id
 */
const updateAIService = async (req, res) => {
  const { id } = req.params;
  const {
    service_name,
    base_url,
    api_key,
    model_name,
    request_format,
    response_format
  } = req.body;

  try {
    const service = await AIServiceConfig.findByPk(id);

    if (!service) {
      return errorResponse(res, 'AI服务配置不存在', 404);
    }

    // 更新服务信息
    if (service_name) service.service_name = service_name;
    if (base_url) service.base_url = base_url;
    if (model_name) service.model_name = model_name;

    // 如果更新API密钥，需要加密
    if (api_key) {
      const encryptedKey = encryptApiKey(api_key);
      service.api_key = JSON.stringify(encryptedKey);
    }

    // 更新可选字段
    if (request_format !== undefined) service.request_format = request_format;
    if (response_format !== undefined) service.response_format = response_format;

    await service.save();

    return successResponse(res, 'AI服务配置更新成功', {
      service: {
        id: service.id,
        service_name: service.service_name,
        base_url: service.base_url,
        model_name: service.model_name,
        is_active: service.is_active,
        updated_at: service.updated_at
      }
    });
  } catch (error) {
    logger.error('更新AI服务配置失败:', error);
    return errorResponse(res, '更新AI服务配置失败', 500);
  }
};

/**
 * 删除AI服务配置
 * @route DELETE /api/admin/ai-services/:id
 */
const deleteAIService = async (req, res) => {
  const { id } = req.params;

  try {
    const service = await AIServiceConfig.findByPk(id);

    if (!service) {
      return errorResponse(res, 'AI服务配置不存在', 404);
    }

    // 检查是否为当前激活的服务
    if (service.is_active) {
      return errorResponse(res, '不能删除当前激活的AI服务，请先激活其他服务', 400);
    }

    await service.destroy();

    return successResponse(res, 'AI服务配置删除成功');
  } catch (error) {
    logger.error('删除AI服务配置失败:', error);
    return errorResponse(res, '删除AI服务配置失败', 500);
  }
};

/**
 * 设置激活的AI服务
 * @route PUT /api/admin/ai-services/:id/activate
 */
const activateAIService = async (req, res) => {
  const { id } = req.params;

  try {
    // 获取要激活的服务
    const service = await AIServiceConfig.findByPk(id);

    if (!service) {
      return errorResponse(res, 'AI服务配置不存在', 404);
    }

    // 切换服务的激活状态，而不是取消所有其他服务的激活状态
    service.is_active = !service.is_active;
    await service.save();

    // 刷新API密钥池以应用更改
    await refreshApiKeyPool();

    return successResponse(res, service.is_active ? 'AI服务激活成功' : 'AI服务停用成功', {
      service: {
        id: service.id,
        service_name: service.service_name,
        is_active: service.is_active
      }
    });
  } catch (error) {
    logger.error('激活/停用AI服务失败:', error);
    return errorResponse(res, '激活/停用AI服务失败', 500);
  }
};

/**
 * 获取当前激活的AI服务
 * @route GET /api/admin/ai-services/active
 */
const getActiveAIService = async (req, res) => {
  try {
    const activeService = await AIServiceConfig.findOne({
      where: { is_active: true }
    });

    if (!activeService) {
      return successResponse(res, '当前没有激活的AI服务', { service: null });
    }

    return successResponse(res, '获取当前激活的AI服务成功', {
      service: {
        id: activeService.id,
        service_name: activeService.service_name,
        base_url: activeService.base_url,
        model_name: activeService.model_name,
        is_active: true,
        created_at: activeService.created_at,
        updated_at: activeService.updated_at
      }
    });
  } catch (error) {
    logger.error('获取当前激活的AI服务失败:', error);
    return errorResponse(res, '获取当前激活的AI服务失败', 500);
  }
};

/**
 * 测试AI服务连接
 * @route POST /api/admin/ai-services/:id/test
 */
const testAIService = async (req, res) => {
  const { id } = req.params;

  try {
    const service = await AIServiceConfig.findByPk(id);

    if (!service) {
      return errorResponse(res, 'AI服务配置不存在', 404);
    }

    logger.info(`开始测试AI服务: ${service.service_name}, ID: ${id}`);

    // 解密API密钥
    let apiKeyData = service.api_key;
    try {
      if (apiKeyData.startsWith('"') && apiKeyData.endsWith('"')) {
        apiKeyData = JSON.parse(apiKeyData);
      }
    } catch (e) {
      logger.warn(`API密钥格式解析失败: ${service.id}`, e);
    }

    const apiKey = await decryptApiKey(apiKeyData);

    // 创建测试任务ID
    const testTaskId = `test_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    // 从数据库获取可配置参数，如果没有则使用默认值
    let aiParameters = {
      temperature: 0.7,
      max_tokens: 8192,
      stream: false
    };

    if (service.parameters) {
      try {
        const dbParameters = JSON.parse(service.parameters);
        aiParameters = { ...aiParameters, ...dbParameters, stream: false };
      } catch (error) {
        logger.warn('解析AI服务参数失败，使用默认参数:', error);
      }
    }

    // 使用与实际业务完全相同的generateContent函数
    const result = await generateContent({
      messages: [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: "Hello! This is a test." }
      ],
      options: aiParameters,
      taskId: testTaskId,
      // 传递服务信息，这样不需要修改数据库中的激活状态
      serviceOverride: {
        id: service.id,
        serviceName: service.service_name,
        baseUrl: service.base_url,
        apiKey: apiKey,
        modelName: service.model_name,
        requestFormat: service.request_format ? JSON.parse(service.request_format) : null,
        responseFormat: service.response_format ? JSON.parse(service.response_format) : null,
        maxConcurrentRequests: service.max_concurrent_requests || 5,
        priority: service.priority || 1,
        weight: service.weight || 1
      }
    });

    // 提取并记录实际使用的服务信息
    const usedServiceInfo = result.ai_service;
    logger.info(`测试请求使用的服务: ${usedServiceInfo?.name || 'unknown'}, 模型: ${usedServiceInfo?.model || 'unknown'}`);

    if (result.success) {
      logger.info(`AI服务测试成功: ${service.service_name}`);
      return successResponse(res, 'AI服务连接测试成功', {
        testResult: {
          success: true,
          message: `${service.service_name} API连接成功`,
          service: usedServiceInfo,
          response: result.data
        }
      });
    } else {
      logger.error(`AI服务测试失败: ${result.error}`);
      return errorResponse(res, `AI服务连接测试失败: ${result.error}`, 400);
    }
  } catch (error) {
    logger.error(`测试AI服务连接失败: ${error.message}`, error);
    return errorResponse(res, `测试AI服务连接失败: ${error.message}`, 500);
  }
};

/**
 * 获取API密钥池状态
 * @route GET /api/admin/ai-services/key-pool
 */
const getKeyPoolStatus = async (req, res) => {
  try {
    const status = getApiKeyPoolStatus();
    return successResponse(res, 'API密钥池状态获取成功', status);
  } catch (error) {
    logger.error('获取API密钥池状态失败:', error);
    return errorResponse(res, '获取API密钥池状态失败', 500);
  }
};

/**
 * 刷新API密钥池
 * @route POST /api/admin/ai-services/refresh-key-pool
 */
const refreshKeyPool = async (req, res) => {
  try {
    const count = await refreshApiKeyPool();
    return successResponse(res, 'API密钥池刷新成功', { count });
  } catch (error) {
    logger.error('刷新API密钥池失败:', error);
    return errorResponse(res, '刷新API密钥池失败', 500);
  }
};

/**
 * 批量更新AI服务状态
 * @route PUT /api/admin/ai-services/batch-update
 */
const batchUpdateStatus = async (req, res) => {
  const { serviceIds, isActive } = req.body;

  if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
    return errorResponse(res, '请提供有效的服务ID列表', 400);
  }

  try {
    // 批量更新服务状态
    const updateResult = await AIServiceConfig.update(
      { is_active: !!isActive },
      { where: { id: serviceIds } }
    );

    // 刷新API密钥池以应用更改
    await refreshApiKeyPool();

    logger.info(`批量${isActive ? '启用' : '停用'}AI服务成功，影响行数：${updateResult[0]}`);

    return successResponse(res, `批量${isActive ? '启用' : '停用'}AI服务成功`, {
      updatedCount: updateResult[0]
    });
  } catch (error) {
    logger.error(`批量${isActive ? '启用' : '停用'}AI服务失败:`, error);
    return errorResponse(res, `批量${isActive ? '启用' : '停用'}AI服务失败`, 500);
  }
};

/**
 * 获取所有AI服务商名称
 * @route GET /api/admin/ai-services/providers
 */
const getServiceProviders = async (req, res) => {
  try {
    // 直接执行原始 SQL 查询获取不同的服务商
    const [results, metadata] = await sequelize.query(
      "SELECT DISTINCT service FROM ai_service_configs WHERE service IS NOT NULL AND service != ''"
    );

    // 提取服务商名称
    const providerNames = results.map(p => p.service);

    return successResponse(res, '获取服务商列表成功', { providers: providerNames });
  } catch (error) {
    logger.error('获取服务商列表失败:', error);
    return errorResponse(res, '获取服务商列表失败', 500);
  }
};

/**
 * AI聊天测试
 * @route POST /api/admin/ai-services/:id/chat-test
 */
const chatTestAIService = async (req, res) => {
  const { id } = req.params;
  const { message, files = [] } = req.body;

  try {
    const service = await AIServiceConfig.findByPk(id);

    if (!service) {
      return errorResponse(res, 'AI服务配置不存在', 404);
    }

    logger.info(`开始AI聊天测试: ${service.service_name}, ID: ${id}`);

    // 解密API密钥
    let apiKeyData = service.api_key;
    try {
      if (apiKeyData.startsWith('"') && apiKeyData.endsWith('"')) {
        apiKeyData = JSON.parse(apiKeyData);
      }
    } catch (e) {
      logger.warn(`API密钥格式解析失败: ${service.id}`, e);
    }

    const apiKey = await decryptApiKey(apiKeyData);

    // 创建测试任务ID
    const testTaskId = `chat_test_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    // 构建消息内容
    let messageContent = message || '这是一个测试消息';

    // 如果有文件，添加文件信息到消息中
    if (files && files.length > 0) {
      const fileInfo = files.map(file => `文件: ${file.name} (${file.type}, ${file.size} bytes)`).join('\n');
      messageContent += `\n\n附件信息:\n${fileInfo}`;
    }

    // 从数据库获取可配置参数，如果没有则使用默认值
    let aiParameters = {
      temperature: 0.7,
      max_tokens: 8192,
      stream: false
    };

    if (service.parameters) {
      try {
        const dbParameters = JSON.parse(service.parameters);
        aiParameters = { ...aiParameters, ...dbParameters, stream: false };
      } catch (error) {
        logger.warn('解析AI服务参数失败，使用默认参数:', error);
      }
    }

    // 使用与实际业务完全相同的generateContent函数
    const result = await generateContent({
      messages: [
        { role: "system", content: "You are a helpful assistant. Please respond in Chinese." },
        { role: "user", content: messageContent }
      ],
      options: aiParameters,
      taskId: testTaskId,
      // 传递服务信息，这样不需要修改数据库中的激活状态
      serviceOverride: {
        id: service.id,
        serviceName: service.service_name,
        baseUrl: service.base_url,
        apiKey: apiKey,
        modelName: service.model_name,
        requestFormat: service.request_format ? JSON.parse(service.request_format) : null,
        responseFormat: service.response_format ? JSON.parse(service.response_format) : null,
        maxConcurrentRequests: service.max_concurrent_requests || 5,
        priority: service.priority || 1,
        weight: service.weight || 1
      }
    });

    // 提取并记录实际使用的服务信息
    const usedServiceInfo = result.ai_service;
    logger.info(`聊天测试请求使用的服务: ${usedServiceInfo?.name || 'unknown'}, 模型: ${usedServiceInfo?.model || 'unknown'}`);

    if (result.success) {
      logger.info(`AI聊天测试成功: ${service.service_name}`);
      return successResponse(res, 'AI聊天测试成功', {
        response: result.data,
        service: usedServiceInfo,
        request: {
          message: messageContent,
          files: files,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      logger.error(`AI聊天测试失败: ${result.error}`);
      return errorResponse(res, `AI聊天测试失败: ${result.error}`, 400);
    }
  } catch (error) {
    logger.error(`AI聊天测试失败: ${error.message}`, error);
    return errorResponse(res, `AI聊天测试失败: ${error.message}`, 500);
  }
};

/**
 * 获取AI服务参数配置
 * @route GET /api/admin/ai-services/:id/parameters
 */
const getServiceParameters = async (req, res) => {
  const { id } = req.params;

  try {
    const service = await AIServiceConfig.findByPk(id);

    if (!service) {
      return errorResponse(res, 'AI服务不存在', 404);
    }

    // 解析参数配置
    let parameters = {};
    if (service.parameters) {
      try {
        parameters = JSON.parse(service.parameters);
      } catch (error) {
        logger.warn(`解析服务${id}的参数配置失败:`, error);
      }
    }

    // 根据服务商类型设置默认参数配置
    const serviceName = service.service_name.toLowerCase();
    let defaultParameters = {};

    if (serviceName.includes('qwen') || serviceName.includes('阿里') || serviceName.includes('百炼')) {
      // 阿里百炼参数
      defaultParameters = {
        temperature: 0.7,
        top_p: 0.8,
        top_k: 20,
        max_tokens: 2000,
        repetition_penalty: 1.05,
        presence_penalty: 0.0,
        seed: 1234,
        enable_search: false,
        stop: [],
        response_format: "text",
        result_format: "message",
        incremental_output: false
      };
    } else if (serviceName.includes('豆包') || serviceName.includes('doubao')) {
      // 豆包参数
      defaultParameters = {
        temperature: 0.7,
        top_p: 0.8,
        max_tokens: 2000,
        max_completion_tokens: 2000,
        stream: false,
        stop: []
      };
    } else if (serviceName.includes('deepseek')) {
      // DeepSeek参数
      defaultParameters = {
        temperature: 0.7,
        max_tokens: 8192,
        top_p: 0.8,
        presence_penalty: 0.0,
        frequency_penalty: 0.0
      };
    } else {
      // 通用参数
      defaultParameters = {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 0.8
      };
    }

    // 如果数据库中没有参数，使用默认参数；否则直接使用数据库参数
    const finalParameters = Object.keys(parameters).length > 0 ? parameters : defaultParameters;

    return successResponse(res, '获取参数配置成功', {
      service_id: service.id,
      service_name: service.service_name,
      parameters: finalParameters
    });
  } catch (error) {
    logger.error('获取AI服务参数配置失败:', error);
    return errorResponse(res, '获取参数配置失败', 500);
  }
};

/**
 * 更新AI服务参数配置
 * @route PUT /api/admin/ai-services/:id/parameters
 */
const updateServiceParameters = async (req, res) => {
  const { id } = req.params;
  const { parameters } = req.body;

  try {
    const service = await AIServiceConfig.findByPk(id);

    if (!service) {
      return errorResponse(res, 'AI服务不存在', 404);
    }

    // 验证参数格式
    if (!parameters || typeof parameters !== 'object') {
      return errorResponse(res, '参数格式错误', 400);
    }

    // 根据服务商类型验证参数
    const serviceName = service.service_name.toLowerCase();
    const validatedParameters = {};

    // 通用参数验证
    // temperature: 0-2
    if (parameters.temperature !== undefined) {
      const temp = parseFloat(parameters.temperature);
      if (isNaN(temp) || temp < 0 || temp > 2) {
        return errorResponse(res, 'temperature参数必须在0-2之间', 400);
      }
      validatedParameters.temperature = temp;
    }

    // max_tokens: 100-32000
    if (parameters.max_tokens !== undefined) {
      const tokens = parseInt(parameters.max_tokens);
      if (isNaN(tokens) || tokens < 100 || tokens > 32000) {
        return errorResponse(res, 'max_tokens参数必须在100-32000之间', 400);
      }
      validatedParameters.max_tokens = tokens;
    }

    // top_p: 0-1
    if (parameters.top_p !== undefined) {
      const topP = parseFloat(parameters.top_p);
      if (isNaN(topP) || topP < 0 || topP > 1) {
        return errorResponse(res, 'top_p参数必须在0-1之间', 400);
      }
      validatedParameters.top_p = topP;
    }

    // 阿里百炼特有参数
    if (serviceName.includes('qwen') || serviceName.includes('阿里') || serviceName.includes('百炼')) {
      // top_k: >=0
      if (parameters.top_k !== undefined) {
        const topK = parseInt(parameters.top_k);
        if (isNaN(topK) || topK < 0) {
          return errorResponse(res, 'top_k参数必须大于等于0', 400);
        }
        validatedParameters.top_k = topK;
      }

      // repetition_penalty: >0
      if (parameters.repetition_penalty !== undefined) {
        const penalty = parseFloat(parameters.repetition_penalty);
        if (isNaN(penalty) || penalty <= 0) {
          return errorResponse(res, 'repetition_penalty参数必须大于0', 400);
        }
        validatedParameters.repetition_penalty = penalty;
      }

      // presence_penalty: -2 to 2
      if (parameters.presence_penalty !== undefined) {
        const penalty = parseFloat(parameters.presence_penalty);
        if (isNaN(penalty) || penalty < -2 || penalty > 2) {
          return errorResponse(res, 'presence_penalty参数必须在-2到2之间', 400);
        }
        validatedParameters.presence_penalty = penalty;
      }

      // seed: 0 to 2^31-1
      if (parameters.seed !== undefined) {
        const seed = parseInt(parameters.seed);
        if (isNaN(seed) || seed < 0 || seed > 2147483647) {
          return errorResponse(res, 'seed参数必须在0到2147483647之间', 400);
        }
        validatedParameters.seed = seed;
      }

      // enable_search: boolean
      if (parameters.enable_search !== undefined) {
        validatedParameters.enable_search = Boolean(parameters.enable_search);
      }

      // stop: array
      if (parameters.stop !== undefined) {
        if (Array.isArray(parameters.stop)) {
          validatedParameters.stop = parameters.stop;
        } else if (typeof parameters.stop === 'string') {
          validatedParameters.stop = [parameters.stop];
        }
      }

      // response_format: string
      if (parameters.response_format !== undefined) {
        if (['text', 'json_object'].includes(parameters.response_format)) {
          validatedParameters.response_format = parameters.response_format;
        }
      }

      // result_format: string
      if (parameters.result_format !== undefined) {
        if (['text', 'message'].includes(parameters.result_format)) {
          validatedParameters.result_format = parameters.result_format;
        }
      }

      // incremental_output: boolean
      if (parameters.incremental_output !== undefined) {
        validatedParameters.incremental_output = Boolean(parameters.incremental_output);
      }
    }

    // 豆包特有参数
    if (serviceName.includes('豆包') || serviceName.includes('doubao')) {
      // stream: boolean
      if (parameters.stream !== undefined) {
        validatedParameters.stream = Boolean(parameters.stream);
      }

      // max_completion_tokens: integer
      if (parameters.max_completion_tokens !== undefined) {
        const tokens = parseInt(parameters.max_completion_tokens);
        if (isNaN(tokens) || tokens < 100 || tokens > 32000) {
          return errorResponse(res, 'max_completion_tokens参数必须在100-32000之间', 400);
        }
        validatedParameters.max_completion_tokens = tokens;
      }

      // stop: array
      if (parameters.stop !== undefined) {
        if (Array.isArray(parameters.stop)) {
          validatedParameters.stop = parameters.stop;
        } else if (typeof parameters.stop === 'string') {
          validatedParameters.stop = [parameters.stop];
        }
      }
    }

    // DeepSeek特有参数
    if (serviceName.includes('deepseek')) {
      // presence_penalty: -2 to 2
      if (parameters.presence_penalty !== undefined) {
        const penalty = parseFloat(parameters.presence_penalty);
        if (isNaN(penalty) || penalty < -2 || penalty > 2) {
          return errorResponse(res, 'presence_penalty参数必须在-2到2之间', 400);
        }
        validatedParameters.presence_penalty = penalty;
      }

      // frequency_penalty: -2 to 2
      if (parameters.frequency_penalty !== undefined) {
        const penalty = parseFloat(parameters.frequency_penalty);
        if (isNaN(penalty) || penalty < -2 || penalty > 2) {
          return errorResponse(res, 'frequency_penalty参数必须在-2到2之间', 400);
        }
        validatedParameters.frequency_penalty = penalty;
      }
    }

    // 更新服务配置
    // 先从数据库加载现有的参数配置
    let existingParams = {};
    if (service.parameters) {
      try {
        existingParams = JSON.parse(service.parameters);
      } catch (e) {
        logger.warn(`解析服务${id}的现有参数配置失败:`, e);
      }
    }

    // 特殊处理seed参数 - 如果前端明确不传递seed参数，则从参数中删除
    // 这样seed参数才能真正成为可选的
    if ('seed' in parameters === false && existingParams.seed !== undefined) {
      // 前端没有传递seed参数，但现有配置中存在，需要删除
      delete validatedParameters.seed;
    } else if (parameters.seed === null || parameters.seed === undefined) {
      // 前端明确将seed设为null或undefined，则删除此参数
      delete validatedParameters.seed;
    }

    // 合并参数，新参数覆盖旧参数
    const finalParameters = { ...existingParams, ...validatedParameters };
    service.parameters = JSON.stringify(finalParameters);
    await service.save();

    logger.info(`更新AI服务${id}参数配置成功`);

    return successResponse(res, '参数配置更新成功', {
      service_id: service.id,
      service_name: service.service_name,
      parameters: finalParameters
    });
  } catch (error) {
    logger.error('更新AI服务参数配置失败:', error);
    return errorResponse(res, '更新参数配置失败', 500);
  }
};

module.exports = {
  getAllAIServices,
  getAIService,
  createAIService,
  updateAIService,
  deleteAIService,
  activateAIService,
  getActiveAIService,
  testAIService,
  getKeyPoolStatus,
  refreshKeyPool,
  batchUpdateStatus,
  getServiceProviders,
  chatTestAIService,
  getServiceParameters,
  updateServiceParameters
};
