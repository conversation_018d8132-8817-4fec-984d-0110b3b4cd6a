const { CoverR<PERSON><PERSON>, User, StylePrompt, BasePrompt, GenerationTask } = require('../models');
const { successResponse, errorResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');
const { sequelize } = require('../config/database');
const { buildPrompt } = require('../utils/promptBuilder');
const { generateContent, extractHtmlFromResponse, cancelTask, getActiveAIService } = require('../utils/aiServiceManager');
const pointsService = require('../services/pointsService');
const LogService = require('../services/logService');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { customAlphabet } = require('nanoid');
const { Op } = require('sequelize');
const util = require('util');

// 调用AI生成接口
const callAiGenerationApi = async (prompt) => {
  try {
    // 设置上传目录
    const uploadDir = path.join(__dirname, '../../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // 获取当前激活的AI服务配置
    let aiParameters = {};
    try {
      const aiService = await getActiveAIService();
      
      // 从AI服务配置中获取参数
      if (aiService.parameters) {
        try {
          if (typeof aiService.parameters === 'string') {
            aiParameters = JSON.parse(aiService.parameters);
          } else {
            aiParameters = aiService.parameters;
          }
          logger.info(`使用AI服务配置的参数生成封面: ${JSON.stringify(aiParameters)}`);
        } catch (error) {
          logger.error(`解析AI服务参数失败: ${error.message}`, error);
          // 使用默认参数
          aiParameters = { temperature: 0.7, max_tokens: 2000 };
          logger.info(`使用默认参数生成封面: ${JSON.stringify(aiParameters)}`);
        }
      } else {
        // 如果没有配置参数，使用默认参数
        aiParameters = { temperature: 0.7, max_tokens: 2000 };
        logger.info(`AI服务未配置参数，使用默认参数生成封面: ${JSON.stringify(aiParameters)}`);
      }
    } catch (error) {
      logger.error(`获取AI服务配置失败: ${error.message}`, error);
      // 使用默认参数
      aiParameters = { temperature: 0.7, max_tokens: 2000 };
      logger.info(`获取AI服务配置失败，使用默认参数生成封面: ${JSON.stringify(aiParameters)}`);
    }

    // 调用AI服务生成内容
    const result = await generateContent({
      messages: [
        { role: "system", content: "You are a helpful assistant that generates HTML cover designs." },
        { role: "user", content: prompt }
      ],
      options: aiParameters
    });

    if (!result.success) {
      throw new Error(`AI生成失败: ${result.error}`);
    }

    // 从响应中提取HTML内容
    const html = extractHtmlFromResponse(result.data);

    // 生成随机图片文件名
    const fileName = `cover_${customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)()}.jpg`;
    const filePath = path.join(uploadDir, fileName);

    // 创建一个空白图片作为封面图片
    try {
      // 创建一个1x1像素的空白图片
      fs.writeFileSync(filePath, Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'));
      logger.info(`创建了空白图片作为封面: ${filePath}`);
    } catch (imgError) {
      logger.error(`创建空白图片失败: ${imgError.message}`, imgError);
      // 如果创建失败，使用默认URL
      imageUrl = '/images/default-cover.jpg';
    }

    let imageUrl = `/uploads/${fileName}`;

    return {
      html,
      imageUrl,
      rawResponse: result.data,
      serviceInfo: result.service
    };
  } catch (error) {
    logger.error('AI生成封面失败:', error);
    throw new Error('调用AI接口失败');
  }
};

/**
 * 获取所有封面风格列表
 * @route GET /api/cover/styles
 */
const getStyleList = async (req, res) => {
  try {
    const styles = await StylePrompt.findAll({
      order: [['display_order', 'ASC']]
    });

    return successResponse(res, '获取风格列表成功', { styles });
  } catch (error) {
    logger.error('获取封面风格列表失败:', error);
    return errorResponse(res, '获取风格列表失败', 500);
  }
};

/**
 * 获取单个风格
 * @route GET /api/cover/style/:id
 */
const getStyleById = async (req, res) => {
  const { id } = req.params;

  try {
    let style;

    // 如果id是数字，使用主键ID查询
    if (!isNaN(parseInt(id))) {
      style = await StylePrompt.findByPk(id);
    } else {
      // 如果不是数字，先尝试使用id_code查询
      style = await StylePrompt.findOne({
        where: { id_code: id }
      });

      // 如果没找到，再尝试使用style_name查询
      if (!style) {
        style = await StylePrompt.findOne({
          where: { style_name: id }
        });
      }
    }

    if (!style) {
      return errorResponse(res, '风格不存在', 404);
    }

    return successResponse(res, '获取风格成功', { style });
  } catch (error) {
    logger.error(`获取风格${id}失败:`, error);
    return errorResponse(res, '获取风格失败', 500);
  }
};

/**
 * 获取封面基础提示词
 * @route GET /api/cover/base-prompts
 */
const getBasePrompts = async (req, res) => {
  const { cover_type, id_code } = req.query;

  try {
    const where = {};
    if (cover_type) {
      where.cover_type = cover_type;
    }
    if (id_code) {
      where.id_code = id_code;
    }

    // 添加缓存控制头，允许客户端缓存结果5分钟
    res.set('Cache-Control', 'public, max-age=300'); // 5分钟缓存

    // 添加ETag支持，允许客户端使用条件请求
    const etag = req.headers['if-none-match'];

    // 查询数据库
    const basePrompts = await BasePrompt.findAll({ where });

    // 生成新的ETag (简单实现，实际可能需要更复杂的哈希算法)
    const newEtag = `W/"${Buffer.from(JSON.stringify(basePrompts)).toString('base64').substring(0, 20)}"`;

    // 如果客户端发送了ETag且匹配，返回304 Not Modified
    if (etag && etag === newEtag) {
      return res.status(304).end();
    }

    // 设置新的ETag
    res.set('ETag', newEtag);

    return successResponse(res, '获取基础提示词成功', { basePrompts });
  } catch (error) {
    logger.error('获取基础提示词失败:', error);
    return errorResponse(res, '获取基础提示词失败', 500);
  }
};

/**
 * 生成封面
 * @route POST /api/cover/generate
 */
const generateCover = async (req, res) => {
  const {
    cover_type,
    cover_style,
    cover_text,
    account_name,
    subtitle,
    auto_title
  } = req.body;
  const userId = req.user.id;

  try {
    const user = req.user;

    // 查询所需的提示词模板
    const basePrompt = await BasePrompt.findOne({
      where: { id_code: cover_type }, // 使用cover_type作为id_code查询，兼容新的字段结构
      order: [['id', 'DESC']] // 使用最新的模板
    });

    if (!basePrompt) {
      return errorResponse(res, `未找到${cover_type}类型的基础提示词模板`, 404);
    }

    // 先尝试使用id_code查询风格
    let stylePrompt = await StylePrompt.findOne({
      where: { id_code: cover_style }
    });

    // 如果没找到，再尝试使用style_name查询
    if (!stylePrompt) {
      stylePrompt = await StylePrompt.findOne({
        where: { style_name: cover_style }
      });
    }

    if (!stylePrompt) {
      return errorResponse(res, `未找到${cover_style}风格的提示词模板`, 404);
    }

    // 构建提示词
    const userInput = { cover_text, account_name, subtitle, auto_title };
    const fullPrompt = buildPrompt(basePrompt, stylePrompt, userInput);

    // 调用AI生成接口
    const { html, imageUrl, rawResponse, serviceInfo } = await callAiGenerationApi(fullPrompt);

    // 使用积分服务扣除积分
    const consumeResult = await pointsService.consumePointsForOperation(
      userId,
      'generate_cover',
      `生成${cover_type}类型封面`
    );

    if (!consumeResult.success) {
      return errorResponse(res, consumeResult.message, 400);
    }

    // 生成唯一的 cover_code
    const coverCode = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)();

    // 创建封面记录，使用数据库中的实际值
    const coverRecord = await CoverRecord.create({
      user_id: userId,
      cover_code: coverCode, // 添加cover_code字段
      cover_type: cover_type, // 使用传入的参数作为主要标识，保持与之前代码一致
      cover_type_name: basePrompt.cover_type, // 封面类型的显示名称
      cover_style: cover_style, // 使用传入的参数作为风格标识，保持与之前代码一致
      cover_style_name: stylePrompt.style_name, // 风格的显示名称
      cover_text,
      account_name: account_name || null,
      subtitle: subtitle || null,
      auto_title: auto_title || false,
      image_url: imageUrl,
      html_content: html, // 添加HTML内容
      prompt_used: fullPrompt,
      ai_service: serviceInfo ? JSON.stringify(serviceInfo) : null
    });

    // 记录到系统日志表
    await LogService.createLog({
      user_id: userId,
      username: req.user.nickname || req.user.phone,
      action: 'create',
      module: 'cover',
      description: `生成${cover_type}类型封面，风格：${cover_style}`,
      status: 'success',
      level: 'info',
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
      source: 'frontend' // 添加来源字段
    });

    logger.info(`用户${userId}成功生成${cover_type}类型封面，使用服务: ${serviceInfo?.name || 'unknown'}`);

    return successResponse(res, '封面生成成功', {
      id: coverRecord.id,
      html,
      image_url: imageUrl,
      points_remaining: consumeResult.points,
      points_consumed: consumeResult.consumed,
      service_info: serviceInfo,
      raw_response: process.env.NODE_ENV === 'development' ? rawResponse : undefined
    });
  } catch (error) {
    logger.error('生成封面失败:', error);
    return errorResponse(res, '生成封面失败，请稍后再试', 500);
  }
};

/**
 * 获取封面详情
 * @route GET /api/cover/:id
 */
const getCoverDetail = async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;

  try {
    const cover = await CoverRecord.findOne({
      where: { id, user_id: userId }
    });

    if (!cover) {
      return errorResponse(res, '封面记录不存在', 404);
    }

    // 检查是否有cover_code，如果没有则生成一个
    if (!cover.cover_code) {
      // 生成唯一的 cover_code
      const coverCode = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)();

      // 更新记录
      cover.cover_code = coverCode;
      await cover.save();

      logger.info(`为封面记录 ${id} 生成了新的 cover_code: ${coverCode}`);
    }

    // 如果数据库中有存储的HTML内容，直接使用，否则重新生成
    let html = cover.html_content;
    if (!html) {
      // 如果没有存储的HTML内容，调用AI接口重新生成
      const generatedResult = await callAiGenerationApi(cover.prompt_used);
      html = generatedResult.html;

      // 更新记录，保存生成的HTML内容
      cover.html_content = html;
      await cover.save();
    }

    // 转换为JSON，便于添加额外字段
    const coverData = cover.toJSON();

    return successResponse(res, '获取封面详情成功', {
      ...coverData,
      html_content: html, // 保持与model字段一致
      html: html // 兼容旧版前端代码
    });
  } catch (error) {
    logger.error('获取封面详情失败:', error);
    return errorResponse(res, '获取封面详情失败', 500);
  }
};

/**
 * 删除封面记录
 * @route DELETE /api/cover/:id
 */
const deleteCover = async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;

  try {
    const cover = await CoverRecord.findOne({
      where: { id, user_id: userId }
    });

    if (!cover) {
      return errorResponse(res, '封面记录不存在', 404);
    }

    await cover.destroy();

    logger.info(`用户${userId}删除封面记录${id}成功`);

    return successResponse(res, '删除封面记录成功');
  } catch (error) {
    logger.error('删除封面记录失败:', error);
    return errorResponse(res, '删除封面记录失败', 500);
  }
};

/**
 * 生成封面HTML（不保存记录，不扣除积分）
 * @route POST /api/cover/generate-html
 */
const generateCoverHtml = async (req, res) => {
  // !!! DEBUG LOGGING START !!!
  logger.debug('[coverController.generateCoverHtml] Entry - Full req.body:', util.inspect(req.body, { depth: null, colors: false }));
  // !!! DEBUG LOGGING END !!!
  try {
    // 安全修复：强制要求用户已登录
    if (!req.user) {
      return errorResponse(res, '请先登录', 401);
    }

    // 从 req.body 中获取 taskId 和 formData
    // 这是当前请求的数据源，必须确保后续逻辑使用这里的 formData
    const { taskId: currentTaskId, formData: currentFormData } = req.body;
    
    const userId = req.user.id;
    const userInfo = {
      user_id: userId,
      role: req.user.role || 'user',
      nickname: req.user.nickname || req.user.phone || '',
      phone: req.user.phone || ''
    };

    // 验证 currentFormData
    if (!currentFormData) {
      logger.warn('生成封面HTML失败: 缺少formData参数');
      return errorResponse(res, '缺少表单数据参数', 400);
    }
    if (!currentFormData.sizeType || !currentFormData.styleId) {
      logger.warn('生成封面HTML失败: formData中缺少sizeType或styleId');
      return errorResponse(res, 'formData中缺少必要参数', 400);
    }

    // !!! DEBUG LOGGING START !!!
    logger.debug('[coverController.generateCoverHtml] Using currentFormData:', util.inspect(currentFormData, { depth: null, colors: false }));
    // !!! DEBUG LOGGING END !!!

    // 查询基础提示词模板 - 使用 currentFormData.sizeType
    const basePromptRecord = await BasePrompt.findOne({
      where: { id_code: currentFormData.sizeType },
      order: [['id', 'DESC']]
    });

    if (!basePromptRecord) {
      logger.error(`未找到 ${currentFormData.sizeType} 类型的基础提示词模板`);
      return errorResponse(res, `未找到 ${currentFormData.sizeType} 类型的基础提示词模板`, 404);
    }

    // 查询风格提示词模板 - 使用 currentFormData.styleId
    let stylePromptRecord = await StylePrompt.findOne({
      where: { id_code: currentFormData.styleId }
    });
    if (!stylePromptRecord) {
      stylePromptRecord = await StylePrompt.findOne({ // Fallback to search by style_name if id_code not found
        where: { style_name: currentFormData.styleId }
      });
      if (!stylePromptRecord) {
        logger.error(`未找到 ${currentFormData.styleId} 风格的提示词模板`);
        return errorResponse(res, `未找到 ${currentFormData.styleId} 风格的提示词模板`, 404);
      }
    }
    
    // 准备userInput给buildPrompt，明确使用 currentFormData
    const userInput = {
      cover_text: currentFormData.coverText,
      account_name: currentFormData.accountName,
      subtitle: currentFormData.subtitle,
      auto_title: currentFormData.autoOptimize, // Ensure field name consistency or map correctly
      // Pass through all custom fields from currentFormData if buildPrompt expects them
      custom_size_enabled: currentFormData.custom_size_enabled,
      custom_width: currentFormData.custom_width,
      custom_height: currentFormData.custom_height,
      custom_style_description: currentFormData.custom_style_description,
      custom_style_enhanced: currentFormData.custom_style_enhanced,
      custom_image_url: currentFormData.custom_image_url,
      custom_image_type: currentFormData.custom_image_type
    };

    // !!! DEBUG LOGGING START !!!
    logger.debug('[coverController.generateCoverHtml] Calling buildPrompt with userInput (derived from currentFormData):', util.inspect(userInput, { depth: null, colors: false }));
    // !!! DEBUG LOGGING END !!!
    
    // 在后端重新构建提示词
    const finalPromptString = buildPrompt(basePromptRecord, stylePromptRecord, userInput);

    if (!finalPromptString) {
      logger.error('在后端构建提示词失败，生成的提示词为空');
      return errorResponse(res, '构建提示词失败', 500);
    }
    
    logger.debug(`后端生成封面HTML，最终提示词长度: ${finalPromptString.length}字符`);
    // !!! DEBUG LOGGING START !!!
    logger.debug('[coverController.generateCoverHtml] Generated finalPromptString (first 500 chars):', finalPromptString ? finalPromptString.substring(0, 500) : 'null or empty');
    // !!! DEBUG LOGGING END !!!

    const generationTaskIdToUse = currentTaskId || `task_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    const clientInfo = {
      ip: req.ip,
      userAgent: req.headers['user-agent']
    };

    const paramSummaryForRecord = currentFormData ? {
      coverText: currentFormData.coverText ? currentFormData.coverText.substring(0, 50) : null,
      accountName: currentFormData.accountName,
      subtitle: currentFormData.subtitle,
      styleId: currentFormData.styleId,
      sizeType: currentFormData.sizeType,
      autoOptimize: currentFormData.autoOptimize
      // Add other relevant fields from currentFormData for logging/record
    } : null;
    
    let aiParameters = {};
    try {
      const activeAiService = await getActiveAIService();
      if (activeAiService.parameters) {
        aiParameters = typeof activeAiService.parameters === 'string' ? JSON.parse(activeAiService.parameters) : activeAiService.parameters;
        logger.info(`使用AI服务配置的参数生成封面HTML: ${JSON.stringify(aiParameters)}`);
      } else {
        aiParameters = { temperature: 0.7, max_tokens: 4096 }; // Default
        logger.info(`AI服务未配置参数，使用默认参数生成封面HTML: ${JSON.stringify(aiParameters)}`);
      }
    } catch (serviceError) {
      logger.error(`获取AI服务配置失败: ${serviceError.message}`, serviceError);
      aiParameters = { temperature: 0.7, max_tokens: 4096 }; // Default
      logger.info(`获取AI服务配置失败，使用默认参数生成封面HTML: ${JSON.stringify(aiParameters)}`);
    }

    // !!! DEBUG LOGGING START !!!
    logger.debug('[coverController.generateCoverHtml] Calling generateContent with finalPromptString (first 500 chars):', finalPromptString ? finalPromptString.substring(0, 500) : 'null or empty');
    // !!! DEBUG LOGGING END !!!
    const result = await generateContent({
      messages: [
        { role: "system", content: "You are a helpful assistant that generates HTML cover designs." },
        { role: "user", content: finalPromptString } // Use the backend-generated prompt
      ],
      options: aiParameters,
      taskId: generationTaskIdToUse,
      userId: userInfo.user_id,
      parameters: { // Store a summary of parameters used for this generation
        formDataSnapshot: paramSummaryForRecord, // Store a snapshot of formData used
        aiParametersUsed: aiParameters
      },
      clientInfo: JSON.stringify(clientInfo)
    });

    if (!result.success) {
      logger.error(`生成封面HTML失败 (AI Service): ${result.error}`);
      return errorResponse(res, `生成失败: ${result.error}`, 500);
    }

    const htmlContent = extractHtmlFromResponse(result.data);
    let coverRecord = null;

    logger.info(`生成封面HTML - 用户信息: ${util.inspect(userInfo, { depth: null, colors: false })}`);
    logger.info(`生成封面HTML - 本次使用的formData (currentFormData): ${util.inspect(currentFormData, { depth: null, colors: false })}`);
    logger.info(`生成封面HTML - 任务ID: ${generationTaskIdToUse}`);

    if (userInfo.user_id && currentFormData) {
      try {
        logger.info(`尝试为用户 ${userInfo.user_id} 创建封面记录, task ID: ${generationTaskIdToUse}`);
        const uploadDir = path.join(__dirname, '../../uploads');
        if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir, { recursive: true });
        
        const imageFileName = `cover_${customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)()}.jpg`;
        let imageUrl = `/uploads/${imageFileName}`;
        const imagePath = path.join(uploadDir, imageFileName);

        try {
          fs.writeFileSync(imagePath, Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'));
          logger.info(`创建了空白图片作为封面: ${imagePath}`);
        } catch (imgError) {
          logger.error(`创建空白图片失败: ${imgError.message}`, imgError);
          imageUrl = '/images/default-cover.jpg';
        }

        let storableHtmlContent = htmlContent;
        if (storableHtmlContent && storableHtmlContent.length > 65535) {
          storableHtmlContent = storableHtmlContent.substring(0, 65535);
          logger.warn(`html_content过长，已截断至65535字符 for task ${generationTaskIdToUse}`);
        }

        const coverCode = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)();
        const now = new Date();
        
        // Make sure basePromptRecord and stylePromptRecord are the ones fetched using currentFormData
        logger.info(`封面记录创建参数: 用户ID=${userInfo.user_id}, 封面类型=${basePromptRecord.cover_type}, 风格=${stylePromptRecord.id_code || stylePromptRecord.style_name}, 任务ID: ${generationTaskIdToUse}`);
        
        coverRecord = await CoverRecord.create({
          user_id: userInfo.user_id,
          cover_code: coverCode,
          cover_type: currentFormData.sizeType,
          cover_type_name: basePromptRecord.cover_type,
          cover_style: currentFormData.styleId,
          cover_style_name: stylePromptRecord.style_name,
          cover_text: currentFormData.coverText,
          account_name: currentFormData.accountName || null,
          subtitle: currentFormData.subtitle || null,
          auto_title: currentFormData.autoOptimize || false, // Check correct field name
          image_url: imageUrl,
          html_content: storableHtmlContent,
          prompt_used: finalPromptString, // Store the actual prompt used
          ai_service: result.ai_service ? JSON.stringify(result.ai_service) : null,
          status: '显示',  // 修改：使用 '显示' 替换 'completed'
          created_at: now,
          updated_at: now
        });
        logger.info(`成功创建封面记录 ID: ${coverRecord.id} for task ${generationTaskIdToUse}`);

        const taskRecordToUpdate = await GenerationTask.findOne({ where: { task_id: generationTaskIdToUse }});
        if (taskRecordToUpdate) {
          taskRecordToUpdate.result_id = coverRecord.id;
          taskRecordToUpdate.cover_type_name = basePromptRecord.cover_type || currentFormData.sizeType || '未知';
          taskRecordToUpdate.style_name = stylePromptRecord.display_name || stylePromptRecord.style_name || currentFormData.styleId || '未知';
          // Ensure 'parameters' in GenerationTask is updated with currentFormData if needed
          // taskRecordToUpdate.parameters = JSON.stringify(currentFormData); // Example if you want to overwrite
          await taskRecordToUpdate.save();
          logger.info(`已更新任务记录 ${generationTaskIdToUse} with result_id ${coverRecord.id}`);
        }

      } catch (dbError) {
        logger.error(`创建/更新封面或任务记录失败 for task ${generationTaskIdToUse}: ${dbError.message}`, dbError);
        // Potentially log to a more persistent error store or notify admins
      }
    } else {
      logger.info(`不创建封面记录 for task ${generationTaskIdToUse}，原因: ${!userInfo.user_id ? '用户未登录' : '表单数据为空'}`);
    }

    // !!! DEBUG LOGGING START !!!
    logger.debug(`[coverController.generateCoverHtml] Final currentFormData before response for task ${generationTaskIdToUse}:`, util.inspect(currentFormData, { depth: null, colors: false }));
    // !!! DEBUG LOGGING END !!!
    return successResponse(res, '生成成功', {
      html: htmlContent,
      rawResponse: result.data,
      taskId: generationTaskIdToUse,
      serviceInfo: result.ai_service,
      record_id: coverRecord ? coverRecord.id : null,
      cover_code: coverRecord ? coverRecord.cover_code : null
    });

  } catch (error) { // This is the main catch block for generateCoverHtml
    logger.error('生成封面HTML主流程失败:', error);
    const errorDetails = {
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      taskId: req.body ? req.body.taskId : 'N/A', 
      formData: req.body ? util.inspect(req.body.formData, { depth: 2, colors: false }) : 'N/A'
    };
    return errorResponse(res, '生成失败，请稍后再试', 500, errorDetails);
  }
};

/**
 * 取消封面生成任务
 * @route POST /api/cover/cancel
 */
const cancelGenerateCover = async (req, res) => {
  // 从请求体或Blob中提取任务ID和取消原因
  let taskId;
  let cancelReason = '用户请求取消';

  // 内容类型检查 - 支持JSON和FormData
  const contentType = req.headers['content-type'] || '';

  if (contentType.includes('application/json')) {
    // 从JSON请求体中获取taskId和cancelReason
    taskId = req.body.taskId;
    if (req.body.reason) {
      cancelReason = req.body.reason;
    } else if (req.body.cancelReason) {
      cancelReason = req.body.cancelReason;
    }
  } else if (contentType.includes('multipart/form-data')) {
    // 从表单数据中获取taskId和cancelReason
    taskId = req.body.taskId;
    if (req.body.reason) {
      cancelReason = req.body.reason;
    } else if (req.body.cancelReason) {
      cancelReason = req.body.cancelReason;
    }
  } else if (contentType.includes('text/plain')) {
    // 尝试解析纯文本内容
    try {
      const data = JSON.parse(req.body);
      taskId = data.taskId;
      if (data.reason) {
        cancelReason = data.reason;
      } else if (data.cancelReason) {
        cancelReason = data.cancelReason;
      }
    } catch (e) {
      taskId = req.body.taskId;
    }
  } else {
    // 其他内容类型，尝试直接访问
    taskId = req.body.taskId;
    if (req.body.reason) {
      cancelReason = req.body.reason;
    } else if (req.body.cancelReason) {
      cancelReason = req.body.cancelReason;
    }
  }

  // 检查请求头中是否包含User-Agent，用于识别请求来源
  const userAgent = req.headers['user-agent'] || '';
  if (userAgent.includes('Mozilla')) {
    // 浏览器请求
    cancelReason = `浏览器请求取消: ${cancelReason}`;
  } else if (userAgent.includes('Node.js')) {
    // Node.js请求
    cancelReason = `Node.js请求取消: ${cancelReason}`;
  } else if (userAgent === '') {
    // 可能是sendBeacon请求
    cancelReason = `可能是sendBeacon请求: ${cancelReason}`;
  }

  // 添加请求方法和IP信息
  cancelReason = `${cancelReason} [${req.method}] [${req.ip}]`;

  if (!taskId) {
    logger.warn('取消封面生成请求缺少任务ID', {
      contentType,
      userAgent,
      body: typeof req.body === 'object' ? '对象' : typeof req.body,
      bodyLength: req.body ? JSON.stringify(req.body).length : 0
    });
    return errorResponse(res, '缺少任务ID', 400);
  }

  try {
    logger.info(`收到取消封面生成请求, 任务ID: ${taskId}, 内容类型: ${contentType}, 原因: ${cancelReason}`);

    // 检查任务是否已经完成
    const { GenerationTask } = require('../models');
    const taskRecord = await GenerationTask.findOne({
      where: { task_id: taskId }
    });

    if (taskRecord && taskRecord.status === 'completed') {
      logger.info(`任务 ${taskId} 已完成，无需取消`);
      return successResponse(res, '任务已完成，无需取消', { exists: true, status: 'completed' });
    }

    // 尝试取消任务
    const isCanceled = await cancelTask(taskId, cancelReason);

    if (isCanceled) {
      return successResponse(res, '已取消封面生成任务');
    } else {
      // 任务可能已经完成或者不存在，但这不视为错误
      return successResponse(res, '任务不存在或已完成', { exists: false });
    }
  } catch (error) {
    logger.error(`取消封面生成任务失败: ${taskId}, 原因: ${cancelReason}`, error);
    return errorResponse(res, '取消任务失败，请稍后再试', 500);
  }
};

/**
 * 获取单个封面记录详情（支持二次编辑）
 * @route GET /api/cover/:id
 */
const getCover = async (req, res) => {
  const coverId = req.params.id;
  const userId = req.user.id;

  if (!coverId) {
    return res.status(400).json({
      success: false,
      message: '缺少封面记录ID参数'
    });
  }

  try {
    logger.info(`获取封面记录详情，ID: ${coverId}, 用户ID: ${userId}`);

    // 查询封面记录
    const coverRecord = await CoverRecord.findOne({
      where: {
        id: coverId,
        user_id: userId // 确保只能查看自己的封面记录
      }
    });

    if (!coverRecord) {
      logger.warn(`未找到封面记录或无权限, ID: ${coverId}, 用户ID: ${userId}`);
      return res.status(404).json({
        success: false,
        message: '找不到对应的封面记录，或您无权查看该记录'
      });
    }

    // 获取风格信息
    let styleInfo = null;
    if (coverRecord.cover_style) {
      try {
        logger.debug(`查询风格信息, cover_style: ${coverRecord.cover_style}`);
        styleInfo = await StylePrompt.findOne({
          where: {
            [Op.or]: [
              { id_code: coverRecord.cover_style },
              { id: isNaN(parseInt(coverRecord.cover_style, 10)) ? -1 : parseInt(coverRecord.cover_style, 10) }
            ]
          }
        });

        if (styleInfo) {
          logger.debug(`找到风格信息: ${styleInfo.style_name}`);
        } else {
          logger.warn(`未找到风格信息, cover_style: ${coverRecord.cover_style}`);
        }
      } catch (styleError) {
        logger.error(`查询风格信息失败: ${styleError.message}`);
        // 错误处理但继续执行
      }
    }

    // 获取尺寸类型信息
    let typeInfo = null;
    if (coverRecord.cover_type) {
      try {
        logger.debug(`查询封面类型信息, cover_type: ${coverRecord.cover_type}`);
        typeInfo = await BasePrompt.findOne({
          where: {
            [Op.or]: [
              { cover_type: coverRecord.cover_type },
              { id: isNaN(parseInt(coverRecord.cover_type, 10)) ? -1 : parseInt(coverRecord.cover_type, 10) }
            ]
          }
        });

        if (typeInfo) {
          logger.debug(`找到封面类型信息: ${typeInfo.cover_type_name}`);
        } else {
          logger.warn(`未找到封面类型信息, cover_type: ${coverRecord.cover_type}`);
        }
      } catch (typeError) {
        logger.error(`查询封面类型信息失败: ${typeError.message}`);
        // 错误处理但继续执行
      }
    }

    // 构建响应数据
    const coverData = {
      id: coverRecord.id,
      user_id: coverRecord.user_id,
      // 优先返回编辑后的HTML内容，如果有的话
      html_content: coverRecord.edited_html_content || coverRecord.html_content || '',
      // 同时保存原始内容和编辑后的内容，以便前端可以根据需要使用
      original_html_content: coverRecord.html_content || '',
      edited_html_content: coverRecord.edited_html_content || '',
      has_edited: !!coverRecord.edited_html_content,
      version: coverRecord.version || 1,
      last_edited_at: coverRecord.last_edited_at,
      cover_style: coverRecord.cover_style || '',
      cover_style_name: coverRecord.cover_style_name || (styleInfo ? styleInfo.style_name : null) || coverRecord.cover_style || '',
      cover_type: coverRecord.cover_type || '',
      cover_type_name: coverRecord.cover_type_name || (typeInfo ? typeInfo.cover_type_name : null) || coverRecord.cover_type || '',
      cover_text: coverRecord.cover_text || '',
      account_name: coverRecord.account_name || '',
      subtitle: coverRecord.subtitle || '',
      auto_title: coverRecord.auto_title || false,
      created_at: coverRecord.created_at
    };

    logger.info(`成功获取封面记录详情, ID: ${coverId}`);
    return res.json({
      success: true,
      data: coverData
    });
  } catch (error) {
    logger.error(`获取封面记录详情失败, ID: ${coverId}`, { error: error.message, stack: error.stack });
    return res.status(500).json({
      success: false,
      message: '获取封面记录详情失败',
      error: error.message
    });
  }
};

/**
 * 获取用户封面记录
 * @route GET /api/user/covers
 */
const getUserCovers = async (req, res) => {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // 查询条件：只返回当前用户的记录，并且状态为"显示"的记录
    const where = {
      user_id: userId,
      status: '显示'
    };

    // 获取总记录数
    const count = await CoverRecord.count({ where });

    // 获取分页数据
    const covers = await CoverRecord.findAll({
      where,
      order: [['created_at', 'DESC']],
      limit,
      offset,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['nickname', 'username', 'phone', 'role']
        }
      ]
    });

    // 处理返回数据
    const formattedCovers = covers.map(cover => {
      const record = cover.toJSON();

      // 处理封面类型显示名称
      if (record.cover_type === 'wechat') {
        record.cover_type_display = '微信公众号';
      } else if (record.cover_type === 'xiaohongshu') {
        record.cover_type_display = '小红书';
      } else {
        record.cover_type_display = record.cover_type_name || record.cover_type;
      }

      // 处理风格显示名称
      record.style_display_name = record.cover_style_name || record.cover_style;

      return record;
    });

    return successResponse(res, '获取封面记录成功', {
      records: formattedCovers,
      pagination: {
        total: count,
        page,
        limit,
        total_pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    logger.error('获取用户封面记录失败:', error);
    return errorResponse(res, '获取封面记录失败', 500);
  }
};

/**
 * 更新封面记录状态（显示/隐藏）
 * @route PATCH /api/admin/cover-records/:id/status
 */
const updateCoverStatus = async (req, res) => {
  try {
    const coverId = req.params.id;
    const { status } = req.body;

    if (!coverId) {
      return errorResponse(res, '缺少封面记录ID', 400);
    }

    if (!status || !['显示', '隐藏'].includes(status)) {
      return errorResponse(res, '状态参数无效，必须是"显示"或"隐藏"', 400);
    }

    // 查找封面记录
    const coverRecord = await CoverRecord.findByPk(coverId);

    if (!coverRecord) {
      return errorResponse(res, '封面记录不存在', 404);
    }

    // 更新状态
    await coverRecord.update({ status });

    return successResponse(res, `封面记录状态已更新为"${status}"`, { id: coverId, status });
  } catch (error) {
    logger.error('更新封面记录状态失败:', error);
    return errorResponse(res, '更新封面记录状态失败', 500);
  }
};
/**
 * 删除封面记录（管理员）
 * @route DELETE /api/admin/cover-records/:id
 */
const deleteCoverRecord = async (req, res) => {
  try {
    const coverId = req.params.id;

    if (!coverId) {
      return errorResponse(res, '缺少封面记录ID', 400);
    }

    // 查找封面记录
    const coverRecord = await CoverRecord.findByPk(coverId);

    if (!coverRecord) {
      return errorResponse(res, '封面记录不存在', 404);
    }

    // 直接删除记录（物理删除）
    await coverRecord.destroy();

    return successResponse(res, '封面记录已成功删除', { id: coverId });
  } catch (error) {
    logger.error('删除封面记录失败:', error);
    return errorResponse(res, '删除封面记录失败', 500);
  }
};

/**
 * 隐藏用户封面记录（用户删除）
 * @route PUT /api/user/covers/:id/hide
 */
const hideCoverRecord = async (req, res) => {
  try {
    const coverId = req.params.id;
    const userId = req.user.id;

    if (!coverId) {
      return errorResponse(res, '缺少封面记录ID', 400);
    }

    // 查找用户的封面记录
    const coverRecord = await CoverRecord.findOne({
      where: {
        id: coverId,
        user_id: userId
      }
    });

    if (!coverRecord) {
      return errorResponse(res, '封面记录不存在或不属于当前用户', 404);
    }

    // 更新状态为隐藏
    await coverRecord.update({ status: '隐藏' });

    return successResponse(res, '封面记录已成功隐藏', { id: coverId });
  } catch (error) {
    logger.error('隐藏封面记录失败:', error);
    return errorResponse(res, '隐藏封面记录失败', 500);
  }
};

/**
 * 管理员获取封面记录列表
 * @route GET /api/admin/cover-records
 */
const getAdminCoverRecords = async (req, res) => {
  try {
    const { page = 1, limit = 10, keyword, cover_type, style, start_date, end_date, sort_order = 'desc' } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 构建查询条件
    const where = {};

    if (keyword) {
      where[Op.or] = [
        { '$user.phone$': { [Op.like]: `%${keyword}%` } },
        { '$user.nickname$': { [Op.like]: `%${keyword}%` } }
      ];
    }

    if (cover_type) {
      where.cover_type = cover_type;
    }

    if (style) {
      where.cover_style = style;
    }

    // 处理日期查询
    if (start_date && end_date) {
      where.created_at = {
        [Op.between]: [
          new Date(`${start_date}T00:00:00.000Z`),
          new Date(`${end_date}T23:59:59.999Z`)
        ]
      };
    } else if (start_date) {
      where.created_at = {
        [Op.gte]: new Date(`${start_date}T00:00:00.000Z`)
      };
    } else if (end_date) {
      where.created_at = {
        [Op.lte]: new Date(`${end_date}T23:59:59.999Z`)
      };
    }

    // 确保sort_order是有效值
    const validSortOrder = ['asc', 'desc'].includes(sort_order) ? sort_order : 'desc';

    // 查询封面记录
    const { count, rows } = await CoverRecord.findAndCountAll({
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'phone', 'nickname', 'role']
        }
      ],
      where,
      order: [['created_at', validSortOrder]],
      limit: parseInt(limit),
      offset,
      distinct: true
    });

    // 转换为前端需要的格式
    const records = rows.map((record, index) => {
      // 使用 toJSON() 获取序列化后的数据
      const recordData = record.toJSON();

      // 构建用户对象 (从原始 record 获取关联数据)
      const user = {
        id: record.user?.id || '',
        phone: record.user?.phone || '',
        nickname: record.user?.nickname || '',
        role: record.user?.role || ''
      };

      // 从模型数据中直接映射创建时间字段
      const created_at = recordData.createdAt || null;

      // 明确构建返回给前端的对象，直接使用 recordData 中的字段
      return {
        id: recordData.id,
        row_num: offset + index + 1, // 添加行号
        cover_code: recordData.cover_code,
        user: user, // 使用上面构建的用户对象
        cover_type: recordData.cover_type,
        cover_type_name: recordData.cover_type_name,
        cover_style: recordData.cover_style,
        cover_style_name: recordData.cover_style_name,
        created_at: created_at, // 直接从模型中获取
        status: recordData.status,
        image_url: recordData.image_url
        // 确保没有其他逻辑意外覆盖或修改 created_at
      };
    });

    // 使用与前端兼容的格式返回数据
    return res.json({
      success: true,
      data: records,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        total_pages: Math.ceil(count / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('管理员获取封面记录失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取封面记录失败',
      error: error.message
    });
  }
};

/**
 * 批量删除封面记录
 * @route DELETE /api/admin/cover-records/batch
 */
const batchDeleteCoverRecords = async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return errorResponse(res, '请提供有效的封面记录ID列表', 400);
  }

  try {
    const deletedCount = await CoverRecord.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });

    if (deletedCount === 0) {
      logger.warn(`尝试批量删除封面记录，但没有找到任何匹配的记录，ID列表: ${ids.join(',')}`);
      return errorResponse(res, '未找到要删除的记录', 404);
    }

    logger.info(`成功批量删除 ${deletedCount} 条封面记录，ID列表: ${ids.join(',')}`);
    return successResponse(res, `成功删除 ${deletedCount} 条记录`);
  } catch (error) {
    logger.error('批量删除封面记录失败:', error);
    return errorResponse(res, '批量删除失败', 500);
  }
};

/**
 * 批量更新封面记录状态
 * @route PATCH /api/admin/cover-records/batch/status
 */
const batchUpdateCoverStatus = async (req, res) => {
  const { ids, status } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return errorResponse(res, '请提供有效的封面记录ID列表', 400);
  }
  if (!['显示', '隐藏'].includes(status)) {
    return errorResponse(res, '无效的状态值，只能是 "显示" 或 "隐藏"', 400);
  }

  try {
    const [updatedCount] = await CoverRecord.update(
      { status: status },
      {
        where: {
          id: {
            [Op.in]: ids
          }
        }
      }
    );

    if (updatedCount === 0) {
        logger.warn(`尝试批量更新封面状态为 ${status}，但没有找到任何匹配的记录，ID列表: ${ids.join(',')}`);
        // 即使没有记录被更新，也可能不是一个错误（例如，用户选择了一些不存在的ID）
        // 或者状态已经是目标状态，所以返回成功信息但计数为0
        return successResponse(res, `没有记录需要更新状态为 ${status}`);
    }

    logger.info(`成功批量将 ${updatedCount} 条封面记录状态更新为 ${status}，ID列表: ${ids.join(',')}`);
    return successResponse(res, `成功更新 ${updatedCount} 条记录的状态为 ${status}`);
  } catch (error) {
    logger.error('批量更新封面状态失败:', error);
    return errorResponse(res, '批量更新状态失败', 500);
  }
};

/**
 * 获取分享的封面内容
 * @route GET /api/cover/share/:code
 */
const getSharedCover = async (req, res) => {
  const { code } = req.params;

  try {
    // 查询封面记录
    const cover = await CoverRecord.findOne({
      where: { cover_code: code }
    });

    if (!cover) {
      return errorResponse(res, '分享的封面不存在或已失效', 404);
    }

    // 获取HTML内容
    let html = cover.html_content;
    if (!html) {
      // 如果没有HTML内容，返回提示信息
      html = `<div style="text-align:center;padding:40px;font-family:sans-serif;color:#333;">
              <h2>该封面内容不可用</h2>
              <p>原始HTML内容丢失或未生成。</p>
              ${cover.image_url ? `<img src="${cover.image_url}" style="max-width:80%;margin:20px auto;display:block;" />` : ''}
            </div>`;
    }

    // 处理HTML内容中的图片路径
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    html = html.replace(/(src|href)=["'](\/[^"']+)["']/g, `$1="${baseUrl}$2"`);

    return successResponse(res, '获取分享封面成功', html);
  } catch (error) {
    logger.error('获取分享封面失败:', error);
    return errorResponse(res, '获取分享封面失败', 500);
  }
};

/**
 * 获取封面HTML内容
 * @route GET /api/cover/:id/html
 */
const getCoverHtml = async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;

  try {
    // 用原生SQL查找，确保查到数据
    const [cover] = await sequelize.query(
      'SELECT * FROM cover_records WHERE id = ? AND user_id = ? AND status = "显示" LIMIT 1',
      { replacements: [Number(id), Number(userId)], type: sequelize.QueryTypes.SELECT }
    );

    if (!cover) {
      return errorResponse(res, '封面记录不存在', 404);
    }

    // 获取HTML内容，优先使用编辑后的内容（如果有）
    let html = cover.edited_html_content || cover.html_content;
    if (!html) {
      // 如果没有存储的HTML内容，尝试重新生成
      try {
        const { generateContent, extractHtmlFromResponse } = require('../utils/aiServiceManager');
        // 使用保存的提示词重新生成HTML内容
        const result = await generateContent({
          messages: [
            { role: "system", content: "You are a helpful assistant that generates HTML cover designs." },
            { role: "user", content: cover.prompt_used }
          ],
          options: {
            temperature: 0.7,
            max_tokens: 2000
          }
        });
        if (result.success) {
          html = extractHtmlFromResponse(result.data);
          // 更新数据库
          await sequelize.query(
            'UPDATE cover_records SET html_content = ? WHERE id = ?',
            { replacements: [html, cover.id], type: sequelize.QueryTypes.UPDATE }
          );
          logger.info(`为封面${id}重新生成HTML内容成功`);
        } else {
          return errorResponse(res, 'HTML内容生成失败', 500);
        }
      } catch (error) {
        logger.error(`为封面${id}重新生成HTML内容失败:`, error);
        return errorResponse(res, 'HTML内容生成失败', 500);
      }
    }

    // 处理HTML内容中的图片路径
    if (html) {
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      // 增强正则表达式处理更多的URL格式
      html = html.replace(/(src|href)=["'](\/[^"']+)["']/g, `$1="${baseUrl}$2"`);
      html = html.replace(/src=["']http:\/\/[^\/]+:3000\//g, `src="${baseUrl}/`);
      html = html.replace(/(src|href)=["']:3000\//g, `$1="${baseUrl}/`);
      // 处理相对路径
      html = html.replace(/(src|href)=["'](?!http|https|data|\/|#)([^"']+)["']/gi, `$1="${baseUrl}/$2"`);
    } else {
      // 如果HTML内容为空，提供一个默认的显示
      html = `<div style="color:#666; text-align:center; padding:20px;">
              <p>暂无HTML内容</p>
              ${cover.image_url ? `<img src="${cover.image_url}" style="max-width:100%; margin-top:20px; border:1px solid #eee;" />` : ''}
            </div>`;
    }

    return successResponse(res, '获取封面HTML内容成功', html);
  } catch (error) {
    logger.error('获取封面HTML内容失败:', error);
    return errorResponse(res, '获取封面HTML内容失败', 500);
  }
};

/**
 * 上传封面自定义图片
 * @route POST /api/cover/upload-image
 */
const uploadImage = async (req, res) => {
  try {
    if (!req.file) {
      return errorResponse(res, '请选择要上传的图片', 400);
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(req.file.mimetype)) {
      return errorResponse(res, '只支持JPG、PNG、GIF、WEBP格式的图片', 400);
    }

    // 验证文件大小（5MB）
    const maxSize = 5 * 1024 * 1024;
    if (req.file.size > maxSize) {
      return errorResponse(res, '图片大小不能超过5MB', 400);
    }

    // 获取图片类型（背景或插图）
    const imageType = req.body.type || 'background';

    // 构建相对路径，便于前端访问
    const imageUrl = `/uploads/${req.file.filename}`;

    logger.info(`上传封面自定义图片成功: ${imageUrl}, 类型: ${imageType}`);

    return successResponse(res, '图片上传成功', {
      imageUrl: imageUrl,
      imageType: imageType
    });
  } catch (error) {
    logger.error('上传图片失败:', error);
    return errorResponse(res, '上传图片失败', 500);
  }
};

/**
 * 保存编辑后的HTML内容
 * @route POST /api/cover/:id/save
 */
const saveEditedHtml = async (req, res) => {
  const { id } = req.params;
  const { edited_html_content, save_type } = req.body;
  const userId = req.user.id;

  // 记录请求头信息和请求体信息，便于调试
  logger.debug(`保存请求头: ${JSON.stringify(req.headers)}`);
  logger.debug(`保存请求体字段: ${Object.keys(req.body).join(', ')}`);

  // 判断是否为手动保存，默认为自动保存
  const isManualSave = save_type === 'manual';

  // 记录保存类型
  logger.info(`保存类型: ${save_type || 'undefined'}, 解析为: ${isManualSave ? '手动保存' : '自动保存'}`);

  try {
    // 记录请求内容长度，便于调试
    logger.info(`保存封面 ${id} 的HTML内容，内容长度: ${edited_html_content ? edited_html_content.length : 0} 字节`);

    // 验证请求数据
    if (!edited_html_content) {
      return errorResponse(res, '编辑后的HTML内容不能为空', 400);
    }

    // 限制HTML内容大小，避免过大内容导致数据库问题
    const maxSize = 60 * 1024; // 降低到 60KB

    // 简化HTML内容，移除不必要的空格和换行
    let processedHtml = edited_html_content
      .replace(/\s+/g, ' ')  // 将多个空白字符压缩为一个
      .replace(/> </g, '><')  // 移除标签之间的空格
      .replace(/<!--[\s\S]*?-->/g, ''); // 移除注释

    // 记录压缩前后的大小
    logger.info(`HTML压缩: 原始大小 ${edited_html_content.length} 字节, 压缩后 ${processedHtml.length} 字节`);

    if (processedHtml.length > maxSize) {
      logger.warn(`封面 ${id} 的HTML内容还是过大: ${processedHtml.length} 字节`);
      return errorResponse(res, 'HTML内容过大，请简化内容或减少元素数量', 413);
    }

    // 查找封面记录
    const cover = await CoverRecord.findOne({
      where: { id, user_id: userId }
    });

    if (!cover) {
      logger.warn(`用户 ${userId} 尝试保存不存在或无权限的封面 ${id}`);
      return errorResponse(res, '封面记录不存在或无权限访问', 404);
    }

    // 获取当前版本号，确保有效值
    const currentVersion = cover.version || 0;

    // 只有手动保存时才更新版本号
    const newVersion = isManualSave ? currentVersion + 1 : currentVersion;

    // 记录保存类型
    logger.info(`保存类型: ${isManualSave ? '手动保存' : '自动保存'}, 版本号: ${currentVersion} -> ${newVersion}`);

    // 更新封面记录，使用压缩后的HTML内容
    await cover.update({
      edited_html_content: processedHtml, // 使用压缩后的HTML内容
      last_edited_at: new Date(),
      version: newVersion
    });

    // 重新获取更新后的记录，确保数据一致性
    const updatedCover = await CoverRecord.findByPk(id);

    logger.info(`成功保存封面 ${id} 的编辑后HTML内容，新版本: ${newVersion}`);

    // 记录操作日志 - 使用logger替代不存在的LogService.logUserAction方法
    logger.info(`用户 ${userId} 保存了封面 ${id} 的编辑后HTML内容`);

    return successResponse(res, '保存成功', {
      id: updatedCover.id,
      version: updatedCover.version,
      last_edited_at: updatedCover.last_edited_at
    });
  } catch (error) {
    // 记录详细错误信息，包括错误类型和堆栈
    logger.error(`保存封面 ${id} 的编辑后HTML内容失败: ${error.message}`, {
      stack: error.stack,
      userId,
      coverId: id,
      saveType: save_type || 'auto',
      contentLength: edited_html_content ? edited_html_content.length : 0
    });

    // 判断错误类型，提供更精准的错误信息
    if (error.name === 'SequelizeDatabaseError') {
      return errorResponse(res, '数据库错误，可能是内容过大或格式不正确', 500);
    } else if (error.name === 'SequelizeConnectionError') {
      return errorResponse(res, '数据库连接错误，请稍后重试', 500);
    } else if (error.name === 'SequelizeValidationError') {
      return errorResponse(res, '数据验证错误：' + error.message, 400);
    }

    // 默认错误响应
    return errorResponse(res, '保存失败：' + error.message, 500);
  }
};

/**
 * 通过cover_code获取封面详情（用于二次编辑）
 * @route GET /api/cover/code/:code/edit
 */
const getCoverByCode = async (req, res) => {
  const { code } = req.params;
  const userId = req.user.id;

  try {
    const cover = await CoverRecord.findOne({
      where: { cover_code: code, user_id: userId }
    });

    if (!cover) {
      return errorResponse(res, '封面记录不存在或您无权访问', 404);
    }

    // 如果数据库中有存储的HTML内容，直接使用，否则重新生成
    let html = cover.html_content;
    if (!html) {
      // 如果没有存储的HTML内容，调用AI接口重新生成
      const generatedResult = await callAiGenerationApi(cover.prompt_used);
      html = generatedResult.html;

      // 更新记录，保存生成的HTML内容
      cover.html_content = html;
      await cover.save();
    }

    // 转换为JSON，便于添加额外字段
    const coverData = cover.toJSON();

    return successResponse(res, '获取封面详情成功', {
      ...coverData,
      html_content: html, // 保持与model字段一致
      html: html // 兼容旧版前端代码
    });
  } catch (error) {
    logger.error('通过cover_code获取封面详情失败:', error);
    return errorResponse(res, '获取封面详情失败', 500);
  }
};

module.exports = {
  getStyleList,
  getStyleById,
  getBasePrompts,
  generateCover,
  getCoverDetail,
  deleteCover,
  generateCoverHtml,
  cancelGenerateCover,
  getCover,
  updateCoverStatus,
  deleteCoverRecord,
  hideCoverRecord,
  getUserCovers,
  getAdminCoverRecords,
  batchDeleteCoverRecords,
  batchUpdateCoverStatus,
  getSharedCover,
  getCoverHtml,
  uploadImage,
  saveEditedHtml,
  getCoverByCode
};

