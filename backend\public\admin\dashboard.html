<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>封面生成网站 - 管理后台</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <!-- 添加DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap5.min.css">
  <link rel="stylesheet" href="css/dashboard.css">
  <link rel="stylesheet" href="css/custom.css">
  <link rel="stylesheet" href="css/tasks.css">
  <!-- 添加jQuery库引用 -->
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
  <!-- 添加DataTables JS库 -->
  <script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.13.1/js/dataTables.bootstrap5.min.js"></script>
  <style>
    .task-stat-icon {
      font-size: 2rem;
    }
    .task-icon-default {
      color: #6c757d;
    }
    .task-icon-pending {
      color: #0d6efd;
    }
    .task-icon-processing {
      color: #0d6efd;
    }
    .task-icon-completed {
      color: #198754;
    }
    .task-icon-failed {
      color: #dc3545;
    }
    .task-icon-canceled {
      color: #ffc107;
    }

    /* AI聊天界面样式 */
    .ai-chat-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: #ffffff;
      z-index: 1050;
      display: flex;
      flex-direction: column;
    }

    .ai-chat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #e9ecef;
      background: #f8f9fa;
    }

    .ai-chat-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.2rem;
    }

    .ai-chat-body {
      flex: 1;
      display: flex;
      overflow: hidden;
    }

    .ai-chat-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
    }

    .ai-chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 1.5rem;
      background: #fafbfc;
    }

    .ai-chat-welcome {
      text-align: center;
      padding: 3rem 2rem;
      color: #6c757d;
    }

    .ai-chat-welcome-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    .ai-chat-input-container {
      border-top: 1px solid #e9ecef;
      background: white;
    }

    .ai-file-preview {
      padding: 1rem 1.5rem 0;
      border-bottom: 1px solid #e9ecef;
    }

    .ai-file-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .ai-chat-input {
      padding: 1rem 1.5rem;
    }

    .ai-input-wrapper {
      display: flex;
      align-items: flex-end;
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 24px;
      padding: 0.5rem;
      transition: all 0.2s ease;
    }

    .ai-input-wrapper:focus-within {
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .ai-input-field {
      flex: 1;
      border: none;
      background: transparent;
      resize: none;
      outline: none;
      padding: 0.5rem 1rem;
      font-size: 0.95rem;
      line-height: 1.4;
      max-height: 120px;
    }

    .ai-input-actions {
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .ai-action-btn {
      width: 36px;
      height: 36px;
      border: none;
      background: transparent;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #6c757d;
      transition: all 0.2s ease;
    }

    .ai-action-btn:hover {
      background: #e9ecef;
      color: #495057;
    }

    .ai-send-btn {
      width: 36px;
      height: 36px;
      border: none;
      background: #007bff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      transition: all 0.2s ease;
    }

    .ai-send-btn:hover {
      background: #0056b3;
      transform: scale(1.05);
    }

    .ai-send-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
    }

    .ai-chat-sidebar {
      width: 350px;
      border-left: 1px solid #e9ecef;
      background: white;
      display: flex;
      flex-direction: column;
    }

    .ai-sidebar-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #e9ecef;
      background: #f8f9fa;
    }

    .ai-sidebar-header h6 {
      margin: 0;
      color: #495057;
      font-weight: 600;
    }

    .ai-sidebar-content {
      flex: 1;
      overflow-y: auto;
      padding: 1rem;
    }

    .ai-sidebar-placeholder {
      text-align: center;
      padding: 2rem 1rem;
      color: #6c757d;
    }

    .ai-sidebar-placeholder i {
      font-size: 2rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    /* 聊天消息样式 */
    .ai-message {
      margin-bottom: 1.5rem;
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .ai-message.user {
      flex-direction: row-reverse;
    }

    .ai-message-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      font-size: 0.9rem;
    }

    .ai-message.user .ai-message-avatar {
      background: #007bff;
      color: white;
    }

    .ai-message.ai .ai-message-avatar {
      background: #6c757d;
      color: white;
    }

    .ai-message-content {
      max-width: 85%;
      background: white;
      border-radius: 18px;
      padding: 12px 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
      position: relative;
      overflow: visible;
      word-break: break-word;
      border: 1px solid #e1e5e9;
    }

    .ai-message.user .ai-message-content {
      background: #007bff;
      color: white;
      border: 1px solid #0056b3;
    }

    .ai-message.ai .ai-message-content {
      background: #f8f9fa;
      color: #333;
      border: 1px solid #e9ecef;
    }

    .ai-message-text {
      margin: 0;
      line-height: 1.6;
      word-wrap: break-word;
      white-space: pre-wrap;
      overflow: visible;
      max-height: none;
      text-overflow: none;
      display: block;
      width: 100%;
      font-size: 14px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    .ai-message.user .ai-message-text {
      color: white;
    }

    .ai-message.ai .ai-message-text {
      color: #333;
    }

    .ai-message-time {
      font-size: 0.75rem;
      opacity: 0.7;
      margin-top: 0.5rem;
    }

    .ai-loading-message {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #6c757d;
      font-style: italic;
    }

    /* API详情样式 */
    .ai-api-section {
      margin-bottom: 1rem;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      overflow: hidden;
    }

    .ai-api-header {
      padding: 0.75rem 1rem;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      font-weight: 600;
      font-size: 0.9rem;
    }

    .ai-api-content {
      padding: 1rem;
    }

    .ai-api-content pre {
      margin: 0;
      font-size: 0.8rem;
      line-height: 1.4;
      background: #f8f9fa;
      padding: 0.75rem;
      border-radius: 4px;
      overflow-x: auto;
    }

    /* 文件预览样式 */
    .ai-file-item {
      position: relative;
      display: inline-block;
      margin-right: 0.5rem;
      margin-bottom: 0.5rem;
    }

    .ai-file-preview {
      width: 60px;
      height: 60px;
      object-fit: cover;
      border-radius: 8px;
      border: 2px solid #dee2e6;
    }

    .ai-file-info {
      display: flex;
      align-items: center;
      padding: 0.5rem;
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      font-size: 0.8rem;
    }

    .ai-file-remove {
      position: absolute;
      top: -8px;
      right: -8px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #dc3545;
      color: white;
      border: none;
      font-size: 0.7rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .ai-chat-sidebar {
        display: none;
      }

      .ai-chat-header {
        padding: 0.75rem 1rem;
      }

      .ai-chat-messages {
        padding: 1rem;
      }

      .ai-chat-input {
        padding: 0.75rem 1rem;
      }
    }
  </style>
</head>
<body>
  <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
    <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">封面生成网站管理后台</a>
    <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="w-100"></div>
    <div class="navbar-nav">
      <div class="nav-item text-nowrap">
        <a class="nav-link px-3" href="#" id="logoutBtn">退出登录</a>
      </div>
    </div>
  </nav>

  <div class="container-fluid">
    <div class="row">
      <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
        <div class="sidebar-sticky">
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link active" href="#dashboard" data-page="dashboard">
                <i class="bi bi-speedometer"></i>控制台概览
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#statistics" data-page="statistics">
                <i class="bi bi-bar-chart"></i>数据统计
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#users" data-page="users">
                <i class="bi bi-people"></i>用户管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#covers" data-page="covers">
                <i class="bi bi-images"></i>封面管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#cover-records" data-page="cover-records">
                <i class="bi bi-clock-history"></i>封面记录
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#tasks" data-page="tasks">
                <i class="bi bi-hdd-stack"></i>任务管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#point-records" data-page="point-records">
                <i class="bi bi-credit-card-2-front"></i>积分记录
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#styles" data-page="styles">
                <i class="bi bi-palette"></i>风格管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#features" data-page="features">
                <i class="bi bi-toggles"></i>功能控制
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#ai-services" data-page="ai-services">
                <i class="bi bi-robot"></i>AI服务管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#settings" data-page="settings">
                <i class="bi bi-gear"></i>系统设置
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#logs" data-page="logs">
                <i class="bi bi-journal-text"></i>系统日志
              </a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 offset-md-3 col-lg-10 offset-lg-2 px-md-4">
        <div id="contentArea" class="content">
          <!-- 控制台概览页面 -->
          <div id="dashboard" class="content-page">
            <h2 class="mb-4">控制台概览</h2>
            <div class="row">
              <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                  <i class="bi bi-people"></i>
                  <h3 id="userCount">--</h3>
                  <p>用户总数</p>
                </div>
              </div>
              <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                  <i class="bi bi-images"></i>
                  <h3 id="coverCount">--</h3>
                  <p>封面总数</p>
                </div>
              </div>
              <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                  <i class="bi bi-credit-card"></i>
                  <h3 id="orderCount">--</h3>
                  <p>订单总数</p>
                </div>
              </div>
              <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                  <i class="bi bi-palette"></i>
                  <h3 id="styleCount">--</h3>
                  <p>风格总数</p>
                </div>
              </div>
            </div>
            <div class="row mt-4">
              <div class="col-md-6">
                <div class="card dashboard-card">
                  <div class="card-header">
                    <h5 class="card-title">最近注册用户</h5>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-striped" id="recentUsersTable">
                        <thead>
                          <tr>
                            <th>ID</th>
                            <th>手机号</th>
                            <th>昵称</th>
                            <th>注册时间</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td colspan="4" class="text-center">加载中...</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="card dashboard-card">
                  <div class="card-header">
                    <h5 class="card-title">最近生成封面</h5>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-striped" id="recentCoversTable">
                        <thead>
                          <tr>
                            <th>ID</th>
                            <th>用户ID</th>
                            <th>封面类型</th>
                            <th>生成时间</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td colspan="4" class="text-center">加载中...</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 统计页面 -->
          <div id="statistics" class="content-page d-none">
            <h2 class="mb-4">数据统计</h2>

            <!-- 统计筛选条件 -->
            <div class="card dashboard-card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">筛选条件</h5>
              </div>
              <div class="card-body">
                <form id="statsFilterForm">
                  <div class="row">
                    <div class="col-md-4 mb-3">
                      <label for="statsType" class="form-label">统计类型</label>
                      <select class="form-select" id="statsType" title="选择统计类型">
                        <option value="user">用户统计</option>
                        <option value="cover">封面统计</option>
                        <option value="order">订单统计</option>
                      </select>
                    </div>
                    <div class="col-md-4 mb-3">
                      <label for="statsDateRange" class="form-label">时间范围</label>
                      <select class="form-select" id="statsDateRange" title="选择时间范围">
                        <option value="all">全部时间</option>
                        <option value="today">今日</option>
                        <option value="yesterday">昨日</option>
                        <option value="last7days" selected>最近7天</option>
                        <option value="last30days">最近30天</option>
                        <option value="thisMonth">本月</option>
                        <option value="lastMonth">上月</option>
                        <option value="custom">自定义</option>
                      </select>
                    </div>
                    <div class="col-md-4 mb-3 d-none" id="customDateContainer">
                      <label for="customDateRange" class="form-label">自定义日期</label>
                      <div class="input-group">
                        <input type="date" class="form-control" id="startDate" aria-label="开始日期" title="开始日期" placeholder="开始日期">
                        <span class="input-group-text">至</span>
                        <input type="date" class="form-control" id="endDate" aria-label="结束日期" title="结束日期" placeholder="结束日期">
                      </div>
                    </div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-md-4 mb-3 d-none" id="userTypeFilter">
                      <label for="userType" class="form-label">用户类型</label>
                      <select class="form-select" id="userType" title="选择用户类型">
                        <option value="all">全部用户</option>
                        <option value="user">普通用户</option>
                        <option value="vip">VIP用户</option>
                        <option value="admin">管理员</option>
                      </select>
                    </div>
                    <div class="col-md-4 mb-3 d-none" id="statsTypeFilter">
                      <label for="statsCoverType" class="form-label">封面类型</label>
                      <select class="form-select" id="statsCoverType" aria-label="按封面类型筛选" title="选择封面类型">
                        <option value="">所有封面类型</option>
                        <option value="xiaohongshu">小红书</option>
                        <option value="wechat">微信公众号</option>
                      </select>
                    </div>
                    <div class="col-md-4 mb-3 d-none" id="orderStatusFilter">
                      <label for="orderStatus" class="form-label">订单状态</label>
                      <select class="form-select" id="orderStatus" title="选择订单状态">
                        <option value="all">全部状态</option>
                        <option value="pending">待支付</option>
                        <option value="success">支付成功</option>
                        <option value="failed">支付失败</option>
                        <option value="refunded">已退款</option>
                      </select>
                    </div>
                  </div>
                  <div class="text-center">
                    <button type="submit" class="btn btn-primary">查询</button>
                    <button type="button" class="btn btn-success ms-2" id="exportStatsBtn">导出Excel</button>
                  </div>
                </form>
              </div>
            </div>

            <!-- 统计数据摘要 -->
            <div class="card dashboard-card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">数据摘要</h5>
              </div>
              <div class="card-body">
                <div class="row" id="statsSummary">
                  <div class="col-md-3 mb-3">
                    <div class="card text-white bg-primary">
                      <div class="card-body">
                        <h5 class="card-title" id="summaryTitle1">总数</h5>
                        <p class="card-text fs-2" id="summaryValue1">0</p>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3 mb-3">
                    <div class="card text-white bg-success">
                      <div class="card-body">
                        <h5 class="card-title" id="summaryTitle2">今日新增</h5>
                        <p class="card-text fs-2" id="summaryValue2">0</p>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3 mb-3">
                    <div class="card text-white bg-info">
                      <div class="card-body">
                        <h5 class="card-title" id="summaryTitle3">昨日新增</h5>
                        <p class="card-text fs-2" id="summaryValue3">0</p>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3 mb-3">
                    <div class="card text-white bg-warning">
                      <div class="card-body">
                        <h5 class="card-title" id="summaryTitle4">平均/天</h5>
                        <p class="card-text fs-2" id="summaryValue4">0</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 统计图表 -->
            <div class="card dashboard-card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0" id="chartTitle">趋势图</h5>
              </div>
              <div class="card-body">
                <canvas id="statsChart" height="300"></canvas>
              </div>
            </div>

            <!-- 统计数据表格 -->
            <div class="card dashboard-card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0" id="tableTitle">详细数据</h5>
                <div class="input-group search-box">
                  <input type="text" class="form-control" id="statsSearchInput" placeholder="搜索...">
                  <button class="btn btn-outline-secondary" type="button" id="statsSearchBtn" aria-label="搜索">
                    <i class="bi bi-search"></i>
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped table-hover" id="statsTable">
                    <thead id="statsTableHead">
                      <!-- 表头将根据选择的统计类型动态生成 -->
                    </thead>
                    <tbody id="statsTableBody">
                      <tr>
                        <td colspan="6" class="text-center">请选择统计类型并点击查询</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="mt-3">
                  <nav>
                    <ul class="pagination justify-content-center" id="statsPagination">
                      <!-- 分页控件将动态生成 -->
                    </ul>
                  </nav>
                </div>
              </div>
            </div>
          </div>

          <!-- 其他页面内容会在脚本中动态加载 -->
          <div id="users" class="content-page d-none">
            <h2 class="mb-4">用户管理</h2>
            <div class="card dashboard-card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">用户列表</h5>
                <button class="btn btn-primary btn-sm" id="addUserBtn">添加用户</button>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <div class="input-group">
                      <input type="text" class="form-control" id="userSearchInput" placeholder="搜索手机号或昵称">
                      <button class="btn btn-outline-secondary" type="button" id="userSearchBtn" title="搜索用户">
                        <i class="bi bi-search"></i>
                      </button>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <select class="form-select" id="userRoleFilter" title="按角色筛选" aria-label="按角色筛选用户">
                      <option value="">所有角色</option>
                      <option value="user">普通用户</option>
                      <option value="vip">VIP用户</option>
                      <option value="admin">管理员</option>
                    </select>
                  </div>
                  <div class="col-md-3 text-end">
                    <button class="btn btn-outline-secondary" id="userRefreshBtn">
                      <i class="bi bi-arrow-repeat"></i> 刷新
                    </button>
                  </div>
                </div>
                <div class="table-responsive">
                  <table class="table table-striped" id="usersTable">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>手机号</th>
                        <th>昵称</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>积分</th>
                        <th>VIP到期时间</th>
                        <th>注册时间</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td colspan="9" class="text-center">加载中...</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                  <div class="page-info">
                    共 <span id="userTotalCount">0</span> 条记录
                  </div>
                  <nav>
                    <ul class="pagination" id="userPagination">
                      <!-- 分页控件将动态生成 -->
                    </ul>
                  </nav>
                </div>
              </div>
            </div>

            <!-- 添加/编辑用户模态框 -->
            <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="userModalLabel">添加用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <form id="addUserForm">
                      <input type="hidden" id="userId">
                      <div class="mb-3">
                        <label for="userPhone" class="form-label">手机号</label>
                        <input type="text" class="form-control" id="userPhone" required>
                      </div>
                      <div class="mb-3">
                        <label for="userNickname" class="form-label">昵称</label>
                        <input type="text" class="form-control" id="userNickname" required>
                      </div>
                      <div class="mb-3">
                        <label for="userPassword" class="form-label">密码</label>
                        <input type="password" class="form-control" id="userPassword">
                        <div class="form-text">添加用户时必填，编辑用户时留空表示不修改</div>
                      </div>
                      <div class="mb-3">
                        <label for="userRole" class="form-label">角色</label>
                        <select class="form-control" id="userRole" required title="选择用户角色">
                          <option value="user">普通用户</option>
                          <option value="vip">VIP用户</option>
                          <option value="admin">管理员</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="userPoints" class="form-label">积分</label>
                        <input type="number" class="form-control" id="userPoints" value="0">
                      </div>
                      <div class="mb-3">
                        <label for="userVipExpireDate" class="form-label">VIP到期时间</label>
                        <div class="row">
                          <div class="col-md-6">
                            <input type="date" class="form-control" id="userVipExpireDate" placeholder="yyyy-mm-dd">
                          </div>
                          <div class="col-md-6">
                            <input type="time" class="form-control" id="userVipExpireTime" placeholder="hh:mm">
                          </div>
                        </div>
                        <div class="form-text">仅对VIP用户有效，设置为空表示移除VIP身份</div>
                      </div>
                    </form>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary" id="userModalSubmitBtn">添加</button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 重置密码模态框 -->
            <div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="resetPasswordModalLabel">重置密码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" id="resetPasswordUserId">
                    <div class="mb-3">
                      <label for="newPassword" class="form-label">新密码</label>
                      <input type="password" class="form-control" id="newPassword" required>
                    </div>
                    <div class="mb-3">
                      <label for="confirmPassword" class="form-label">确认密码</label>
                      <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmResetPasswordBtn">确认重置</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div id="covers" class="content-page d-none">
            <h2 class="mb-4">封面管理</h2>
            <div class="card dashboard-card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">封面列表</h5>
                <div>
                  <button type="button" class="btn btn-success btn-sm me-2" id="addCoverBtn">添加封面模板</button>
                  <select class="form-select-sm d-inline-block cover-type-select" id="coverTypeFilter" title="选择封面类型" aria-label="封面类型筛选">
                    <option value="">全部类型</option>
                    <option value="xiaohongshu">小红书</option>
                    <option value="wechat">微信公众号</option>
                  </select>
                  <button class="btn btn-primary btn-sm ms-2" id="coverSearchBtn">搜索</button>
                </div>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped" id="coversTable">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>标识码</th>
                        <th>封面类型</th>
                        <th>封面尺寸</th>
                        <th>提示词内容</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td colspan="6" class="text-center">加载中...</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="mt-3">
                  <nav>
                    <ul class="pagination justify-content-center" id="coversPagination">
                      <li class="page-item disabled"><a class="page-link" href="#">上一页</a></li>
                      <li class="page-item active"><a class="page-link" href="#">1</a></li>
                      <li class="page-item"><a class="page-link" href="#">下一页</a></li>
                    </ul>
                  </nav>
                </div>
              </div>
            </div>

            <!-- 添加/编辑封面模板模态框 -->
            <div class="modal fade" id="coverModal" tabindex="-1" aria-labelledby="coverModalLabel" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="coverModalLabel">添加封面模板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <form id="coverForm">
                      <input type="hidden" id="coverId">
                      <div class="mb-3">
                        <label for="coverIdCode" class="form-label">封面标识码</label>
                        <input type="text" class="form-control" id="coverIdCode" required placeholder="例如: xiaohongshu, wechat 或自定义标识码">
                        <div class="form-text">输入封面模板的唯一标识码，只能包含字母、数字和下划线，例如：xiaohongshu、wechat、custom_type</div>
                      </div>
                      <div class="mb-3">
                        <label for="coverTypeMgmt" class="form-label">封面类型名称</label>
                        <input type="text" class="form-control" id="coverTypeMgmt" required placeholder="例如: 小红书封面, 微信公众号封面">
                        <div class="form-text">输入封面类型的中文名称，用于前端显示</div>
                      </div>
                      <div class="mb-3">
                        <label for="coverSize" class="form-label">封面尺寸</label>
                        <input type="text" class="form-control" id="coverSize" required placeholder="例如: 1080x1080">
                        <div class="form-text">输入封面尺寸，格式为"宽x高"，例如"1080x1080"</div>
                      </div>
                      <div class="mb-3">
                        <label for="promptContentMgmt" class="form-label">提示词模板内容</label>
                        <textarea class="form-control" id="promptContentMgmt" rows="5" required></textarea>
                        <div class="form-text">输入基础提示词模板，用于生成特定类型和尺寸的封面，可以包含变量，例如: {title}、{style}等</div>
                      </div>
                    </form>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveCoverBtn">保存</button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 删除封面确认模态框 -->
            <div class="modal fade" id="deleteCoverModal" tabindex="-1" aria-labelledby="deleteCoverModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="deleteCoverModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <p>确定要删除封面类型 <span id="deleteCoverType"></span> 的模板吗？此操作不可恢复。</p>
                    <input type="hidden" id="deleteCoverId">
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteCoverBtn">删除</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div id="styles" class="content-page d-none">
            <h2 class="mb-4">风格管理</h2>
            <div class="card dashboard-card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">风格列表</h5>
                <button class="btn btn-primary btn-sm" id="addStyleBtn">添加风格</button>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped" id="stylesTable">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>标识码</th>
                        <th>风格名称</th>
                        <th>提示词内容</th>
                        <th>显示顺序</th>
                        <th>状态</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td colspan="7" class="text-center">加载中...</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- 添加/编辑风格模态框 -->
            <div class="modal fade" id="styleModal" tabindex="-1" aria-labelledby="styleModalLabel" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="styleModalLabel">添加风格</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <form id="styleForm">
                      <input type="hidden" id="styleId">
                      <div class="mb-3">
                        <label for="styleIdCode" class="form-label">风格标识码</label>
                        <input type="text" class="form-control" id="styleIdCode" required placeholder="例如: simple_clean, tech_modern">
                        <div class="form-text">输入风格模板的唯一标识码，只能包含字母、数字和下划线</div>
                      </div>
                      <div class="mb-3">
                        <label for="styleName" class="form-label">风格名称</label>
                        <input type="text" class="form-control" id="styleName" required>
                      </div>
                      <div class="mb-3">
                        <label for="stylePromptContent" class="form-label">提示词内容</label>
                        <textarea class="form-control" id="stylePromptContent" rows="5" required></textarea>
                        <div class="form-text">输入有效的提示词，用于指导AI生成特定风格的封面。</div>
                      </div>
                      <div class="mb-3">
                        <label for="displayOrder" class="form-label">显示顺序</label>
                        <input type="number" class="form-control" id="displayOrder" value="0">
                        <div class="form-text">数字越小越靠前显示</div>
                      </div>
                      <div class="mb-3">
                        <label for="stylePreviewImage" class="form-label">风格示例图</label>
                        <input type="file" class="form-control" id="stylePreviewImage" accept="image/*">
                        <div class="form-text">上传一张代表该风格的示例图片（可选）</div>
                        <div id="previewImageContainer" class="mt-2 d-none">
                          <img id="previewImagePreview" class="preview-image" src="" alt="预览图">
                          <button type="button" class="btn btn-sm btn-danger ms-2" id="removePreviewImageBtn">移除</button>
                        </div>
                      </div>
                      <!-- 新增示例HTML输入框 -->
                      <div class="mb-3">
                        <label for="exampleHtml" class="form-label">示例HTML</label>
                        <textarea class="form-control" id="exampleHtml" rows="8"></textarea>
                        <div class="form-text">输入代表该风格的示例HTML代码，将在前台点击风格标签时显示（可选）</div>
                      </div>
                      <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="styleStatus" checked>
                        <label class="form-check-label" for="styleStatus">启用</label>
                      </div>
                    </form>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveStyleBtn">保存</button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 删除确认模态框 -->
            <div class="modal fade" id="deleteStyleModal" tabindex="-1" aria-labelledby="deleteStyleModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="deleteStyleModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <p>确定要删除风格 <span id="deleteStyleName"></span> 吗？此操作不可恢复。</p>
                    <input type="hidden" id="deleteStyleId">
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteStyleBtn">删除</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div id="features" class="content-page d-none">
            <h2 class="mb-4">功能控制</h2>
            <div class="card dashboard-card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">功能控制列表</h5>
                <button class="btn btn-primary btn-sm" id="addFeatureBtn">添加功能控制</button>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <div class="input-group">
                      <input type="text" class="form-control" id="featureSearchInput" placeholder="搜索功能名称">
                      <button class="btn btn-outline-secondary" type="button" id="featureSearchBtn" title="搜索功能">
                        <i class="bi bi-search"></i>
                      </button>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <select class="form-select" id="featureActiveFilter" title="按状态筛选">
                      <option value="">所有状态</option>
                      <option value="true">已启用</option>
                      <option value="false">已禁用</option>
                    </select>
                  </div>
                  <div class="col-md-3 text-end">
                    <button class="btn btn-outline-secondary" id="featureRefreshBtn">
                      <i class="bi bi-arrow-repeat"></i> 刷新
                    </button>
                  </div>
                </div>

                <div class="table-responsive">
                  <table class="table table-striped table-hover" id="featuresTable">
                    <thead>
                      <tr>
                        <th>序号</th>
                        <th>功能名称</th>
                        <th>适用角色</th>
                        <th>积分扣除</th>
                        <th>状态</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td colspan="6" class="text-center">加载中...</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-3">
                  <div>
                    <select class="form-select form-select-sm" id="featurePageSize" title="每页显示条数">
                      <option value="10">10条/页</option>
                      <option value="20">20条/页</option>
                      <option value="50">50条/页</option>
                    </select>
                  </div>
                  <div>
                    <nav>
                      <ul class="pagination pagination-sm" id="featurePagination">
                        <li class="page-item disabled">
                          <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item disabled">
                          <a class="page-link" href="#">下一页</a>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 功能控制模态框 -->
          <div class="modal fade" id="featureModal" tabindex="-1" aria-labelledby="featureModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="featureModalLabel">添加功能控制</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <form id="addFeatureForm">
                    <input type="hidden" id="featureId">
                    <div class="mb-3">
                      <label for="featureName" class="form-label">功能名称</label>
                      <input type="text" class="form-control" id="featureName" required>
                      <div class="form-text">例如：生成封面-积分扣除</div>
                    </div>
                    <div class="mb-3">
                      <label for="featureDescription" class="form-label">功能描述</label>
                      <textarea class="form-control" id="featureDescription" rows="4"></textarea>
                      <div class="form-text">详细描述功能的逻辑和实现方式</div>
                    </div>
                    <div class="mb-3">
                      <label class="form-label">用户权限</label>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="user" id="roleUser">
                        <label class="form-check-label" for="roleUser">
                          普通用户
                        </label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="vip" id="roleVip">
                        <label class="form-check-label" for="roleVip">
                          VIP用户
                        </label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="admin" id="roleAdmin">
                        <label class="form-check-label" for="roleAdmin">
                          管理员
                        </label>
                      </div>
                      <div class="form-text">选择需要应用此功能控制的用户角色</div>
                    </div>
                    <div class="mb-3">
                      <label for="pointsCost" class="form-label">扣除积分</label>
                      <input type="number" class="form-control" id="pointsCost" min="0" value="0">
                      <div class="form-text">用户使用此功能需要扣除的积分数量</div>
                    </div>
                    <div class="mb-3">
                      <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="isActive" checked>
                        <label class="form-check-label" for="isActive">启用功能</label>
                      </div>
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                  <button type="button" class="btn btn-primary" id="featureModalSubmitBtn">保存</button>
                </div>
              </div>
            </div>
          </div>

          <div id="ai-services" class="content-page d-none">
            <h2 class="mb-4">AI服务管理</h2>
            <div class="card dashboard-card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">AI服务列表</h5>
                <div>
                  <button class="btn btn-success btn-sm" id="batchActivateBtn">
                    <i class="bi bi-check-circle"></i> 一键启用
                  </button>
                  <button class="btn btn-warning btn-sm" id="batchDeactivateBtn">
                    <i class="bi bi-x-circle"></i> 一键停用
                  </button>
                  <button class="btn btn-primary btn-sm" id="addAIServiceBtn">
                    <i class="bi bi-plus-circle"></i> 添加AI服务商
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-4">
                    <div class="input-group">
                      <input type="text" class="form-control" id="aiServiceSearchInput" placeholder="搜索AI服务商、AI名、模型...">
                      <button class="btn btn-outline-secondary" type="button" id="aiServiceSearchBtn" title="搜索">
                        <i class="bi bi-search"></i>
                      </button>
                    </div>
                  </div>
                  <div class="col-md-8 d-flex gap-2 justify-content-end">
                    <select class="form-select form-select-sm" id="serviceFilter" style="max-width: 150px;">
                      <option value="">所有服务商</option>
                      <option value="deepseek">deepseek</option>
                      <option value="豆包">豆包</option>
                      <option value="阿里百炼">阿里百炼</option>
                    </select>
                    <select class="form-select form-select-sm" id="priorityFilter" style="max-width: 120px;">
                      <option value="">所有优先级</option>
                      <option value="1">优先级 1</option>
                      <option value="2">优先级 2</option>
                      <option value="3">优先级 3</option>
                      <option value="4">优先级 4</option>
                      <option value="5">优先级 5</option>
                    </select>
                    <select class="form-select form-select-sm" id="weightFilter" style="max-width: 120px;">
                      <option value="">所有权重</option>
                      <option value="1">权重 1</option>
                      <option value="2">权重 2</option>
                      <option value="3">权重 3</option>
                      <option value="4">权重 4</option>
                      <option value="5">权重 5</option>
                    </select>
                    <select class="form-select form-select-sm" id="activeFilter" style="max-width: 120px;">
                      <option value="">所有状态</option>
                      <option value="1">已激活</option>
                      <option value="0">未激活</option>
                    </select>
                    <select class="form-select form-select-sm" id="serviceTypeFilter" style="max-width: 140px;">
                      <option value="">所有服务类型</option>
                      <option value="文本AI">文本AI</option>
                      <option value="图像AI">图像AI</option>
                      <option value="音频AI">音频AI</option>
                    </select>
                  </div>
                </div>
                <div class="table-responsive">
                  <table class="table table-striped" id="aiServicesTable">
                    <thead>
                      <tr>
                        <th class="text-center" width="40">
                          <input type="checkbox" id="selectAllServices" class="form-check-input">
                        </th>
                        <th scope="col">序号</th>
                        <th scope="col">AI服务商</th>
                        <th scope="col">AI名</th>
                        <th scope="col">模型</th>
                        <th scope="col">密钥</th>
                        <th scope="col">是否激活</th>
                        <th scope="col">服务权重</th>
                        <th scope="col">优先级</th>
                        <th scope="col">最大并发</th>
                        <th scope="col">服务类型</th>
                        <th scope="col">操作</th>
                      </tr>
                    </thead>
                    <tbody id="aiServicesTableBody">
                      <tr>
                        <td colspan="12" class="text-center">加载中...</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                  <div>
                    <select class="form-select form-select-sm" id="aiServicesPageSize" title="每页显示条数">
                      <option value="10">10条/页</option>
                      <option value="20">20条/页</option>
                      <option value="50">50条/页</option>
                    </select>
                  </div>
                  <div>
                    <nav>
                      <ul class="pagination pagination-sm" id="aiServicesPagination">
                        <li class="page-item disabled">
                          <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item disabled">
                          <a class="page-link" href="#">下一页</a>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
              </div>
            </div>

            <!-- 添加/编辑AI服务模态框 -->
            <div class="modal fade" id="aiServiceModal" tabindex="-1" aria-labelledby="aiServiceModalLabel" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="aiServiceModalTitle">添加AI服务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <form id="aiServiceForm">
                      <input type="hidden" id="serviceId">
                      <div class="row">
                        <div class="col-md-6 mb-3">
                          <label for="service" class="form-label">AI服务商</label>
                          <select class="form-select" id="service" required>
                            <option value="">请选择服务商</option>
                            <option value="deepseek">deepseek</option>
                            <option value="豆包">豆包</option>
                            <option value="阿里百炼">阿里百炼</option>
                            <option value="other">其他</option>
                          </select>
                          <div class="form-text">选择服务提供商</div>
                        </div>
                        <div class="col-md-6 mb-3">
                          <label for="serviceName" class="form-label">AI名</label>
                          <input type="text" class="form-control" id="serviceName" required>
                          <div class="form-text">例如：DeepSeek-1, doubao-1等</div>
                        </div>
                      </div>
                      <div class="mb-3">
                        <label for="baseUrl" class="form-label">基础URL</label>
                        <input type="text" class="form-control" id="baseUrl" required>
                        <div class="form-text">API基础URL，例如：https://api.deepseek.com/v1</div>
                      </div>
                      <div class="mb-3">
                        <label for="apiKey" class="form-label">API密钥</label>
                        <input type="password" class="form-control" id="apiKey">
                        <div class="form-text" id="apiKeyNote">新增服务时为必填项，编辑时不填则保持原值</div>
                      </div>
                      <div class="mb-3">
                        <label for="modelName" class="form-label">模型名称</label>
                        <input type="text" class="form-control" id="modelName" required>
                        <div class="form-text">例如：deepseek-chat, gpt-4等</div>
                      </div>
                      <div class="row">
                        <div class="col-md-3 mb-3">
                          <label for="weight" class="form-label">服务权重</label>
                          <input type="number" class="form-control" id="weight" value="1" min="1" max="5">
                          <div class="form-text">负载均衡权重，1-5</div>
                        </div>
                        <div class="col-md-3 mb-3">
                          <label for="priority" class="form-label">优先级</label>
                          <input type="number" class="form-control" id="priority" value="1" min="1" max="5">
                          <div class="form-text">优先级，1-5</div>
                        </div>
                        <div class="col-md-3 mb-3">
                          <label for="maxConcurrentRequests" class="form-label">最大并发</label>
                          <input type="number" class="form-control" id="maxConcurrentRequests" value="5" min="0">
                          <div class="form-text">0表示不限制</div>
                        </div>
                        <div class="col-md-3 mb-3">
                          <label for="serviceType" class="form-label">服务类型</label>
                          <input type="text" class="form-control" id="serviceType" value="文本AI">
                          <div class="form-text">例如：文本AI</div>
                        </div>
                      </div>
                      <div class="mb-3">
                        <label for="requestFormat" class="form-label">请求格式模板</label>
                        <textarea class="form-control" id="requestFormat" rows="5"></textarea>
                        <div class="form-text">JSON格式的请求模板，使用${prompt}作为提示词占位符</div>
                      </div>
                      <div class="mb-3">
                        <label for="responseFormat" class="form-label">响应格式处理</label>
                        <textarea class="form-control" id="responseFormat" rows="3"></textarea>
                        <div class="form-text">JSON格式的响应处理方式，指定如何从响应中提取内容</div>
                      </div>
                    </form>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-info" id="testAIService">测试连接</button>
                    <button type="button" class="btn btn-primary" id="saveAIService">保存</button>
                  </div>
                </div>
              </div>
            </div>

            <!-- API密钥池状态模态框 -->
            <div class="modal fade" id="apiKeyPoolModal" tabindex="-1" aria-labelledby="apiKeyPoolModalLabel" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="apiKeyPoolModalLabel">API密钥池状态</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <div class="mb-3 d-flex justify-content-between align-items-center">
                      <h6>当前活跃密钥: <span id="activeKeyCount" class="badge bg-success">0</span></h6>
                      <button type="button" class="btn btn-primary btn-sm" id="refreshKeyPool">
                        <i class="bi bi-arrow-repeat"></i> 刷新密钥池
                      </button>
                    </div>
                    <div class="table-responsive">
                      <table class="table table-striped" id="keyPoolTable">
                        <thead>
                          <tr>
                            <th>服务名称</th>
                            <th>模型名称</th>
                            <th>API密钥(部分)</th>
                            <th>当前使用量</th>
                            <th>总请求数</th>
                            <th>成功率</th>
                            <th>状态</th>
                          </tr>
                        </thead>
                        <tbody id="keyPoolTableBody">
                          <tr>
                            <td colspan="7" class="text-center">加载中...</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div id="settings" class="content-page d-none">
            <h2 class="mb-4">系统设置</h2>
            <div class="row">
              <div class="col-md-6 mb-4">
                <div class="card dashboard-card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">积分设置</h5>
                  </div>
                  <div class="card-body">
                    <form id="pointsSettingsForm">
                      <div class="mb-3">
                        <label for="newUserPoints" class="form-label">新用户初始积分</label>
                        <input type="number" class="form-control" id="newUserPoints" min="0" step="1">
                        <div class="form-text">新用户注册后自动获得的积分数量</div>
                      </div>
                      <div class="mb-3">
                        <label for="coverPointsCost" class="form-label">生成封面所需积分</label>
                        <input type="number" class="form-control" id="coverPointsCost" min="0" step="1">
                        <div class="form-text">用户每次生成封面消耗的积分数量</div>
                      </div>
                      <div class="mb-3">
                        <label for="verifyCodeStorageType" class="form-label">验证码存储方式</label>
                        <select class="form-select" id="verifyCodeStorageType">
                          <option value="memory">内存存储（本地测试环境）</option>
                          <option value="database">数据库存储（云服务器部署）</option>
                        </select>
                        <div class="form-text">内存存储适用于本地测试环境，数据库存储适用于云服务器部署</div>
                      </div>
                      <button type="submit" class="btn btn-primary" id="saveSettingsBtn">保存积分设置</button>
                    </form>
                  </div>
                </div>
              </div>
              <div class="col-md-6 mb-4">
                <div class="card dashboard-card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">网站信息</h5>
                  </div>
                  <div class="card-body">
                    <form id="siteSettingsForm">
                      <div class="mb-3">
                        <label for="siteName" class="form-label">网站名称</label>
                        <input type="text" class="form-control" id="siteName">
                      </div>
                      <div class="mb-3">
                        <label for="siteDescription" class="form-label">网站描述</label>
                        <textarea class="form-control" id="siteDescription" rows="2"></textarea>
                      </div>
                      <div class="mb-3">
                        <label for="contactEmail" class="form-label">联系邮箱</label>
                        <input type="email" class="form-control" id="contactEmail">
                      </div>
                      <div class="mb-3">
                        <label for="icp" class="form-label">ICP备案号</label>
                        <input type="text" class="form-control" id="icp">
                      </div>
                      <button type="submit" class="btn btn-primary" id="siteSettingsSaveBtn">保存网站信息</button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12 mb-4">
                <div class="card dashboard-card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">隐私政策和用户协议</h5>
                  </div>
                  <div class="card-body">
                    <ul class="nav nav-tabs" id="policyTabs" role="tablist">
                      <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="privacy-tab" data-bs-toggle="tab" data-bs-target="#privacy" type="button" role="tab" aria-controls="privacy" aria-selected="true">隐私政策</button>
                      </li>
                      <li class="nav-item" role="presentation">
                        <button class="nav-link" id="agreement-tab" data-bs-toggle="tab" data-bs-target="#agreement" type="button" role="tab" aria-controls="agreement" aria-selected="false">用户协议</button>
                      </li>
                    </ul>
                    <div class="tab-content mt-3" id="policyTabsContent">
                      <div class="tab-pane fade show active" id="privacy" role="tabpanel" aria-labelledby="privacy-tab">
                        <form id="privacyPolicyForm">
                          <div class="mb-3">
                            <label for="privacyPolicyVersion" class="form-label">版本号</label>
                            <input type="text" class="form-control" id="privacyPolicyVersion" placeholder="例如: 1.0">
                          </div>
                          <div class="mb-3">
                            <label for="privacyPolicyContent" class="form-label">隐私政策内容</label>
                            <textarea class="form-control" id="privacyPolicyContent" rows="15" placeholder="请输入隐私政策内容，支持HTML格式"></textarea>
                          </div>
                          <button type="submit" class="btn btn-primary" id="privacyPolicySaveBtn">保存隐私政策</button>
                        </form>
                      </div>
                      <div class="tab-pane fade" id="agreement" role="tabpanel" aria-labelledby="agreement-tab">
                        <form id="userAgreementForm">
                          <div class="mb-3">
                            <label for="userAgreementVersion" class="form-label">版本号</label>
                            <input type="text" class="form-control" id="userAgreementVersion" placeholder="例如: 1.0">
                          </div>
                          <div class="mb-3">
                            <label for="userAgreementContent" class="form-label">用户协议内容</label>
                            <textarea class="form-control" id="userAgreementContent" rows="15" placeholder="请输入用户协议内容，支持HTML格式"></textarea>
                          </div>
                          <button type="submit" class="btn btn-primary" id="userAgreementSaveBtn">保存用户协议</button>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12 mb-4">
                <div class="card dashboard-card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">数据库备份与恢复</h5>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-6">
                        <h6>备份数据库</h6>
                        <p>点击下方按钮创建数据库的完整备份。</p>
                        <button id="backupDatabaseBtn" class="btn btn-primary">
                          <i class="bi bi-download"></i> 备份数据库
                        </button>
                      </div>
                      <div class="col-md-6">
                        <h6>恢复数据库</h6>
                        <p>选择一个备份文件恢复数据库。此操作会覆盖当前数据。</p>
                        <div class="input-group mb-3">
                          <input type="file" class="form-control" id="restoreFileInput" accept=".json">
                          <button id="restoreDatabaseBtn" class="btn btn-warning">
                            <i class="bi bi-upload"></i> 恢复
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 系统日志页面 -->
          <div id="logs" class="content-page d-none">
            <h2 class="mb-4">系统日志</h2>

            <!-- 日志筛选条件 -->
            <div class="card dashboard-card mb-4">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">筛选条件</h5>
                <button class="btn btn-danger btn-sm" id="logCleanupBtn">清理过期日志</button>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-2 mb-3">
                    <label for="logSourceFilter" class="form-label">来源</label>
                    <select class="form-select" id="logSourceFilter" title="选择日志来源">
                      <option value="">全部来源</option>
                      <option value="frontend">前台用户</option>
                      <option value="backend">后台管理</option>
                    </select>
                  </div>
                  <div class="col-md-2 mb-3">
                    <label for="logModuleFilter" class="form-label">模块</label>
                    <select class="form-select" id="logModuleFilter" title="选择日志模块">
                      <option value="">全部模块</option>
                      <option value="users">用户管理</option>
                      <option value="covers">封面管理</option>
                      <option value="styles">风格管理</option>
                      <option value="settings">系统设置</option>
                      <option value="auth">认证</option>
                      <option value="logs">日志管理</option>
                    </select>
                  </div>
                  <div class="col-md-2 mb-3">
                    <label for="logActionFilter" class="form-label">操作类型</label>
                    <select class="form-select" id="logActionFilter" title="选择操作类型">
                      <option value="">全部操作</option>
                      <option value="query">查询</option>
                      <option value="create">创建</option>
                      <option value="update">更新</option>
                      <option value="delete">删除</option>
                      <option value="login">登录</option>
                      <option value="logout">登出</option>
                    </select>
                  </div>
                  <div class="col-md-2 mb-3">
                    <label for="logStatusFilter" class="form-label">状态</label>
                    <select class="form-select" id="logStatusFilter" title="选择日志状态">
                      <option value="">全部状态</option>
                      <option value="success">成功</option>
                      <option value="failure">失败</option>
                    </select>
                  </div>
                  <div class="col-md-2 mb-3">
                    <label for="logUsernameFilter" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="logUsernameFilter" placeholder="输入用户名">
                  </div>
                  <div class="col-md-2 mb-3">
                    <label for="logStartDate" class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="logStartDate" title="开始日期" placeholder="开始日期">
                  </div>
                  <div class="col-md-2 mb-3">
                    <label for="logEndDate" class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="logEndDate" title="结束日期" placeholder="结束日期">
                  </div>
                </div>
                <div class="text-center">
                  <button type="button" class="btn btn-primary" id="logSearchBtn">查询</button>
                  <button type="button" class="btn btn-secondary ms-2" id="logResetBtn">重置</button>
                </div>
              </div>
            </div>

            <!-- 日志统计图表 -->
            <div class="card dashboard-card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">日志统计</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-4 mb-3">
                    <canvas id="moduleChart" height="250"></canvas>
                  </div>
                  <div class="col-md-4 mb-3">
                    <canvas id="actionChart" height="250"></canvas>
                  </div>
                  <div class="col-md-4 mb-3">
                    <canvas id="statusChart" height="250"></canvas>
                  </div>
                </div>
              </div>
            </div>

            <!-- 日志列表 -->
            <div class="card dashboard-card">
              <div class="card-header">
                <h5 class="card-title mb-0">日志列表</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped" id="logsTable">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>用户</th>
                        <th>来源</th>
                        <th>模块</th>
                        <th>操作</th>
                        <th>描述</th>
                        <th>级别</th>
                        <th>状态</th>
                        <th>时间</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody id="logsTableBody">
                      <tr>
                        <td colspan="10" class="text-center">加载中...</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="mt-3 d-flex justify-content-between align-items-center">
                  <div class="d-flex align-items-center">
                    <span class="me-2">每页显示:</span>
                    <select class="form-select form-select-sm" id="logPageSize" style="width: auto;">
                      <option value="10">10条</option>
                      <option value="20">20条</option>
                      <option value="50">50条</option>
                      <option value="100">100条</option>
                    </select>
                  </div>
                  <nav>
                    <ul class="pagination justify-content-center mb-0" id="logsPagination">
                      <!-- 分页控件将动态生成 -->
                    </ul>
                  </nav>
                </div>
              </div>
            </div>

            <!-- 日志详情模态框 -->
            <div class="modal fade" id="logDetailModal" tabindex="-1" aria-labelledby="logDetailModalLabel" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="logDetailModalLabel">日志详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <div class="row mb-3">
                      <div class="col-md-6">
                        <p><strong>ID:</strong> <span id="logDetailId"></span></p>
                        <p><strong>用户:</strong> <span id="logDetailUser"></span></p>
                        <p><strong>来源:</strong> <span id="logDetailSource" class="badge"></span></p>
                        <p><strong>模块:</strong> <span id="logDetailModule"></span></p>
                        <p><strong>操作:</strong> <span id="logDetailAction"></span></p>
                      </div>
                      <div class="col-md-6">
                        <p><strong>描述:</strong> <span id="logDetailDescription"></span></p>
                        <p><strong>级别:</strong> <span id="logDetailLevel" class="badge"></span></p>
                        <p><strong>状态:</strong> <span id="logDetailStatus" class="badge"></span></p>
                        <p><strong>时间:</strong> <span id="logDetailTime"></span></p>
                      </div>
                    </div>
                    <div class="mb-3">
                      <label class="form-label"><strong>详细信息:</strong></label>
                      <pre class="bg-light p-3 rounded log-detail-info" id="logDetailInfo"></pre>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 清理日志模态框 -->
            <div class="modal fade" id="logCleanupModal" tabindex="-1" aria-labelledby="logCleanupModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="logCleanupModalLabel">清理系统日志</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <div class="alert alert-warning">
                      <i class="bi bi-exclamation-triangle-fill me-2"></i>
                      <strong>警告：</strong>此操作将永久删除过期日志，无法恢复！
                    </div>
                    <form>
                      <div class="mb-3">
                        <label for="infoLogsDays" class="form-label">信息级日志保留天数</label>
                        <input type="number" class="form-control" id="infoLogsDays" min="1" value="15" required>
                        <div class="form-text">保留最近多少天的信息级日志，建议15天</div>
                      </div>
                      <div class="mb-3">
                        <label for="warningLogsDays" class="form-label">警告级日志保留天数</label>
                        <input type="number" class="form-control" id="warningLogsDays" min="1" value="30" required>
                        <div class="form-text">保留最近多少天的警告级日志，建议30天</div>
                      </div>
                      <div class="mb-3">
                        <label for="errorLogsDays" class="form-label">错误级日志保留天数</label>
                        <input type="number" class="form-control" id="errorLogsDays" min="1" value="60" required>
                        <div class="form-text">保留最近多少天的错误级日志，建议60天</div>
                      </div>
                      <div class="mb-3">
                        <label for="maxLogsCount" class="form-label">日志最大记录数</label>
                        <input type="number" class="form-control" id="maxLogsCount" min="100" value="10000" required>
                        <div class="form-text">日志表保留的最大记录数，超过此数会额外清理最旧的记录</div>
                      </div>
                    </form>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="logModule.cleanupLogs()">清理日志</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 积分记录管理页面 -->
          <div id="point-records" class="content-page d-none">
            <h2 class="mb-4">积分记录</h2>

            <!-- 筛选条件 -->
            <div class="card dashboard-card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">筛选条件</h5>
              </div>
              <div class="card-body">
                <form id="pointRecordsFilterForm" class="row g-3">
                  <div class="col-md-3">
                    <label for="pointKeyword" class="form-label">关键词</label>
                    <input type="text" class="form-control" id="pointKeyword" placeholder="搜索用户名/昵称">
                  </div>
                  <div class="col-md-3">
                    <label for="pointOperationType" class="form-label">操作类型</label>
                    <select class="form-select" id="pointOperationType" title="选择操作类型">
                      <option value="">全部</option>
                      <option value="register">注册奖励</option>
                      <option value="daily_reward">每日奖励</option>
                      <option value="generate">生成封面</option>
                      <option value="admin_adjust">管理员调整</option>
                      <option value="vip_upgrade">VIP升级</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label for="pointDateRange" class="form-label">日期范围</label>
                    <div class="input-group">
                      <input type="date" class="form-control" id="pointStartDate" title="开始日期" placeholder="开始日期">
                      <span class="input-group-text">至</span>
                      <input type="date" class="form-control" id="pointEndDate" title="结束日期" placeholder="结束日期">
                    </div>
                  </div>
                  <div class="col-md-2 d-flex align-items-end">
                    <div class="d-grid gap-2 w-100">
                      <button type="button" class="btn btn-primary" id="searchPointRecordsBtn">
                        <i class="bi bi-search"></i> 搜索
                      </button>
                      <button type="button" class="btn btn-outline-secondary" id="resetPointRecordsBtn">
                        <i class="bi bi-arrow-repeat"></i> 重置
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>

            <!-- 积分记录表格 -->
            <div class="card dashboard-card">
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped table-hover" id="pointRecordsTable">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>昵称</th>
                        <th>角色</th>
                        <th>积分变动</th>
                        <th>变动后积分</th>
                        <th>操作类型</th>
                        <th>备注</th>
                        <th>操作时间</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- 积分记录将通过JavaScript动态添加 -->
                    </tbody>
                  </table>
                </div>
                <!-- 分页控件 -->
                <nav aria-label="积分记录分页" class="mt-4">
                  <ul class="pagination justify-content-center" id="pointRecordsPagination"></ul>
                </nav>
              </div>
            </div>
          </div>

          <!-- 封面记录管理页面 -->
          <div id="cover-records" class="content-page d-none">
            <h2 class="mb-4">封面记录</h2>

            <!-- 筛选条件 -->
            <div class="card dashboard-card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">筛选条件</h5>
              </div>
              <div class="card-body">
                <form id="coverRecordsFilterForm" class="row g-3">
                  <div class="col-md-3">
                    <label for="coverRecordKeyword" class="form-label">关键词</label>
                    <input type="text" class="form-control" id="coverRecordKeyword" placeholder="搜索用户名/昵称">
                  </div>
                  <div class="col-md-2">
                    <label for="coverRecordType" class="form-label">封面类型</label>
                    <select class="form-select" id="coverRecordType" title="选择封面类型">
                      <option value="">全部</option>
                      <option value="wechat">微信公众号</option>
                      <option value="xiaohongshu">小红书</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label for="coverRecordStyle" class="form-label">风格</label>
                    <select class="form-select" id="coverRecordStyle" title="选择风格">
                      <option value="">全部</option>
                      <!-- 风格选项将通过JavaScript动态添加 -->
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label for="coverRecordDateRange" class="form-label">日期范围</label>
                    <div class="input-group">
                      <input type="date" class="form-control" id="coverRecordStartDate" title="开始日期" placeholder="开始日期">
                      <span class="input-group-text">至</span>
                      <input type="date" class="form-control" id="coverRecordEndDate" title="结束日期" placeholder="结束日期">
                    </div>
                  </div>
                  <div class="col-md-12 d-flex justify-content-end">
                    <button type="button" class="btn btn-primary me-2" id="searchCoverRecordsBtn">
                      <i class="bi bi-search"></i> 搜索
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="resetCoverRecordsBtn">
                      <i class="bi bi-arrow-repeat"></i> 重置
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <!-- 功能控制栏 - 新增在这里 -->
            <div class="mb-3 d-flex justify-content-between align-items-center">
              <div>
                <span id="totalRecordsCount" class="me-3">全部数据：--</span>
                <div class="d-inline-block me-3">
                  <select class="form-select form-select-sm" id="sortOrderSelect" title="排序方式">
                    <option value="desc">时间降序</option>
                    <option value="asc">时间升序</option>
                  </select>
                </div>
                <div class="d-inline-block">
                  <select class="form-select form-select-sm" id="pageSizeSelect" title="每页记录数">
                    <option value="10" selected>每页10条</option>
                    <option value="20">每页20条</option>
                    <option value="50">每页50条</option>
                    <option value="100">每页100条</option>
                  </select>
                </div>
              </div>
              <div id="batchActionsContainer">
                <button id="batchDeleteBtn" class="btn btn-sm btn-danger me-2">批量删除</button>
                <button id="batchHideBtn" class="btn btn-sm btn-warning me-2">批量隐藏</button>
                <button id="batchShowBtn" class="btn btn-sm btn-success">批量显示</button>
              </div>
            </div>

            <!-- 封面记录表格 -->
            <div class="card dashboard-card">
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped table-hover" id="coverRecordsTable">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>昵称</th>
                        <th>角色</th>
                        <th>封面类型</th>
                        <th>风格</th>
                        <th>生成时间</th>
                        <th>状态</th>
                        <th>预览</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- 封面记录将通过JavaScript动态添加 -->
                    </tbody>
                  </table>
                </div>
                <!-- 分页控件 -->
                <nav aria-label="封面记录分页" class="mt-4">
                  <ul class="pagination justify-content-center" id="coverRecordsPagination"></ul>
                </nav>
              </div>
            </div>
          </div>

          <!-- 封面HTML预览模态框 -->
          <div class="modal fade" id="coverHtmlPreviewModal" tabindex="-1" aria-labelledby="coverHtmlPreviewModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="coverHtmlPreviewModalLabel">封面HTML预览</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <div class="row mb-3">
                    <div class="col-md-8">
                      <div class="card">
                        <div class="card-body p-0">
                          <iframe id="coverHtmlPreviewFrame" class="preview-iframe-container" style="width: 100%; height: 500px; border: none;"></iframe>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="card">
                        <div class="card-header">
                          <h6 class="card-title mb-0">封面信息</h6>
                        </div>
                        <div class="card-body">
                          <div id="coverPreviewInfo">
                            <!-- 封面信息将通过JavaScript动态添加 -->
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 任务管理页面 -->
          <div id="tasks" class="content-page d-none">
            <h2 class="mb-4">任务管理</h2>

            <!-- 任务统计卡片 -->
            <div class="row mb-4">
              <div class="col-xl-2 col-md-4">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <i class="bi bi-hdd-stack task-stat-icon task-icon-default"></i>
                    <h5 class="card-title">总任务数</h5>
                    <h3 id="taskTotalCount">--</h3>
                  </div>
                </div>
              </div>
              <div class="col-xl-2 col-md-4">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <i class="bi bi-hourglass task-stat-icon task-icon-pending"></i>
                    <h5 class="card-title">等待中</h5>
                    <h3 id="taskPendingCount">--</h3>
                  </div>
                </div>
              </div>
              <div class="col-xl-2 col-md-4">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <i class="bi bi-arrow-repeat task-stat-icon task-icon-processing"></i>
                    <h5 class="card-title">处理中</h5>
                    <h3 id="taskProcessingCount">--</h3>
                  </div>
                </div>
              </div>
              <div class="col-xl-2 col-md-4">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <i class="bi bi-check-circle task-stat-icon task-icon-completed"></i>
                    <h5 class="card-title">已完成</h5>
                    <h3 id="taskCompletedCount">--</h3>
                  </div>
                </div>
              </div>
              <div class="col-xl-2 col-md-4">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <i class="bi bi-x-circle task-stat-icon task-icon-failed"></i>
                    <h5 class="card-title">失败</h5>
                    <h3 id="taskFailedCount">--</h3>
                  </div>
                </div>
              </div>
              <div class="col-xl-2 col-md-4">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <i class="bi bi-slash-circle task-stat-icon task-icon-canceled"></i>
                    <h5 class="card-title">已取消</h5>
                    <h3 id="taskCanceledCount">--</h3>
                  </div>
                </div>
              </div>
            </div>

            <!-- 任务搜索和筛选 -->
            <div class="card mb-4">
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-2">
                    <label for="taskStatusFilter" class="form-label">任务状态</label>
                    <select class="form-select" id="taskStatusFilter" aria-label="筛选任务状态">
                      <option value="">所有状态</option>
                      <option value="pending">等待中</option>
                      <option value="processing">处理中</option>
                      <option value="completed">已完成</option>
                      <option value="failed">失败</option>
                      <option value="canceled">已取消</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">日期范围</label>
                    <div class="input-group">
                      <span class="input-group-text">从</span>
                      <input type="date" class="form-control" id="taskStartDate" aria-label="开始日期">
                      <span class="input-group-text">至</span>
                      <input type="date" class="form-control" id="taskEndDate" aria-label="结束日期">
                    </div>
                  </div>
                  <div class="col-md-2">
                    <label for="taskUserIdFilter" class="form-label">用户ID</label>
                    <input type="text" class="form-control" id="taskUserIdFilter" placeholder="输入用户ID" aria-label="筛选用户ID">
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div class="btn-group d-block" role="group">
                      <button type="button" class="btn btn-primary" id="taskSearchBtn">
                        <i class="bi bi-search"></i> 搜索
                      </button>
                      <button type="button" class="btn btn-outline-secondary" id="taskResetBtn">
                        <i class="bi bi-arrow-counterclockwise"></i> 重置
                      </button>
                      <button type="button" class="btn btn-danger" id="taskCleanupBtn">
                        <i class="bi bi-trash"></i> 清理记录
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 任务列表 -->
            <div class="card">
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped table-hover">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>任务ID</th>
                        <th>用户</th>
                        <th>任务类型</th>
                        <th>状态</th>
                        <th>使用封面</th>
                        <th>使用风格</th>
                        <th>创建时间</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody id="taskTableBody">
                      <!-- 任务列表将由JS动态加载 -->
                    </tbody>
                  </table>
                </div>

                <!-- 分页控件 -->
                <nav aria-label="任务列表分页">
                  <ul class="pagination justify-content-center" id="taskPagination">
                    <!-- 分页控件将由JS动态加载 -->
                  </ul>
                </nav>
              </div>
            </div>
          </div>

          <!-- 任务详情模态框 -->
          <div class="modal fade" id="taskDetailModal" tabindex="-1" aria-labelledby="taskDetailModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="taskDetailModalLabel">任务详情</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <div class="task-detail-header mb-3">
                    <span class="badge bg-primary me-2" id="taskDetailId"></span>
                    <span class="task-status-badge" id="taskDetailStatus"></span>
                  </div>

                  <div class="row mb-4">
                    <div class="col-md-6">
                      <dl class="row">
                        <dt class="col-sm-4">任务ID</dt>
                        <dd class="col-sm-8" id="taskDetailTaskId"></dd>

                        <dt class="col-sm-4">任务类型</dt>
                        <dd class="col-sm-8" id="taskDetailType"></dd>

                        <dt class="col-sm-4">用户信息</dt>
                        <dd class="col-sm-8" id="taskDetailUser"></dd>

                        <dt class="col-sm-4">开始时间</dt>
                        <dd class="col-sm-8" id="taskDetailStartTime"></dd>

                        <dt class="col-sm-4">结束时间</dt>
                        <dd class="col-sm-8" id="taskDetailEndTime"></dd>
                      </dl>
                    </div>
                    <div class="col-md-6">
                      <dl class="row">
                        <dt class="col-sm-4">执行时长</dt>
                        <dd class="col-sm-8" id="taskDetailDuration"></dd>

                        <dt class="col-sm-4">模型名称</dt>
                        <dd class="col-sm-8" id="taskDetailModelName"></dd>

                        <dt class="col-sm-4">使用封面</dt>
                        <dd class="col-sm-8" id="taskDetailCoverType"></dd>

                        <dt class="col-sm-4">使用风格</dt>
                        <dd class="col-sm-8" id="taskDetailStyleName"></dd>

                        <dt class="col-sm-4">结果ID</dt>
                        <dd class="col-sm-8" id="taskDetailResultId"></dd>
                      </dl>
                    </div>
                  </div>

                  <div id="taskDetailErrorSection" class="d-none mb-4">
                    <h6>错误信息</h6>
                    <div class="alert alert-danger" id="taskDetailError"></div>
                  </div>

                  <div id="taskDetailParamsSection" class="mb-3">
                    <h6>任务参数</h6>
                    <pre class="bg-light p-3 rounded" id="taskDetailParams"></pre>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-danger d-none" id="taskDetailCancelBtn">取消任务</button>
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 任务清理确认模态框 -->
          <div class="modal fade" id="taskCleanupModal" tabindex="-1" aria-labelledby="taskCleanupModalLabel" aria-hidden="true">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="taskCleanupModalLabel">确认清理记录</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <p>此操作将删除所有<strong>已完成</strong>、<strong>失败</strong>或<strong>已取消</strong>的任务记录。</p>
                  <div class="mb-3">
                    <label for="taskCleanupDays" class="form-label">保留最近天数</label>
                    <select class="form-select" id="taskCleanupDays" aria-label="保留最近天数">
                      <option value="7">保留最近7天</option>
                      <option value="15">保留最近15天</option>
                      <option value="30" selected>保留最近30天</option>
                      <option value="90">保留最近90天</option>
                    </select>
                  </div>
                  <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    此操作不可撤销，但不会影响已生成的封面。
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                  <button type="button" class="btn btn-danger" id="confirmTaskCleanupBtn">确认清理</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 任务删除确认模态框 -->
          <div class="modal fade" id="deleteTaskModal" tabindex="-1" aria-labelledby="deleteTaskModalLabel" aria-hidden="true">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="deleteTaskModalLabel">确认删除任务</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <p>确定要删除这个任务吗？此操作无法撤销。</p>
                  <input type="hidden" id="deleteTaskId">
                  <p>任务ID: <span id="deleteTaskIdDisplay"></span></p>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                  <button type="button" class="btn btn-danger" id="confirmDeleteTaskBtn">确认删除</button>
                </div>
              </div>
            </div>
          </div>

          <!-- AI服务删除确认模态框 -->
          <div class="modal fade" id="deleteServiceModal" tabindex="-1" aria-labelledby="deleteServiceModalLabel" aria-hidden="true">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="deleteServiceModalLabel">确认删除AI服务</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <p>确定要删除以下AI服务吗？此操作无法撤销。</p>
                  <input type="hidden" id="deleteServiceId">
                  <p>服务名称: <span id="deleteServiceName"></span></p>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                  <button type="button" class="btn btn-danger" id="confirmDeleteServiceBtn">确认删除</button>
                </div>
              </div>
            </div>
          </div>

          <!-- AI聊天测试界面 -->
          <div class="ai-chat-container d-none" id="aiChatTestModal">
            <div class="ai-chat-header">
              <div class="d-flex align-items-center">
                <div class="ai-chat-avatar me-3">
                  <i class="bi bi-robot"></i>
                </div>
                <div>
                  <h5 class="mb-0">AI服务测试</h5>
                  <small class="text-muted">测试服务: <span id="testServiceName">-</span></small>
                </div>
              </div>
              <div class="ai-chat-controls">
                <button type="button" class="btn btn-outline-secondary btn-sm me-2" id="clearChatBtn">
                  <i class="bi bi-trash"></i> 清空对话
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" id="closeChatBtn">
                  <i class="bi bi-x-lg"></i> 关闭
                </button>
              </div>
            </div>

            <div class="ai-chat-body">
              <!-- 聊天区域 -->
              <div class="ai-chat-main">
                <div class="ai-chat-messages" id="chatMessages">
                  <div class="ai-chat-welcome">
                    <div class="ai-chat-welcome-icon">
                      <i class="bi bi-chat-dots"></i>
                    </div>
                    <h6>开始与AI对话测试</h6>
                    <p class="text-muted">发送消息来测试AI服务的响应</p>
                  </div>
                </div>

                <div class="ai-chat-input-container">
                  <!-- 文件预览区域 -->
                  <div id="filePreviewArea" class="ai-file-preview d-none">
                    <div class="ai-file-list" id="filePreviewList"></div>
                  </div>

                  <!-- 输入区域 -->
                  <div class="ai-chat-input">
                    <div class="ai-input-wrapper">
                      <textarea
                        id="chatInput"
                        placeholder="输入消息..."
                        rows="1"
                        class="ai-input-field"
                      ></textarea>
                      <div class="ai-input-actions">
                        <button type="button" class="ai-action-btn" id="attachFileBtn" title="上传文件">
                          <i class="bi bi-paperclip"></i>
                        </button>
                        <button type="button" class="ai-action-btn" id="attachImageBtn" title="上传图片">
                          <i class="bi bi-image"></i>
                        </button>
                        <button type="button" class="ai-send-btn" id="sendMessageBtn">
                          <i class="bi bi-send"></i>
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- 隐藏的文件输入 -->
                  <input type="file" id="fileInput" class="d-none" multiple accept="*/*">
                  <input type="file" id="imageInput" class="d-none" multiple accept="image/*">
                </div>
              </div>

              <!-- API详情侧边栏 -->
              <div class="ai-chat-sidebar">
                <div class="ai-sidebar-header">
                  <h6><i class="bi bi-code-square"></i> API调用详情</h6>
                </div>
                <div class="ai-sidebar-content" id="apiDetails">
                  <div class="ai-sidebar-placeholder">
                    <i class="bi bi-info-circle"></i>
                    <p>API调用详情将在这里显示</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 正在加载提示 -->
        <div id="loading" class="loading-overlay">
          <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="footer mt-auto py-3 bg-dark text-white">
    <div class="container text-center">
      <span class="text-muted">© 2023 封面生成管理系统. 保留所有权利.</span>
    </div>
  </footer>

  <!-- JavaScript -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="js/utils.js"></script>
  <script src="js/dashboard.js"></script>
</body>
</html>
