// 临时脚本：向style_prompts表添加example_html字段
const { sequelize } = require('./src/config/database');

async function addColumn() {
  try {
    // 首先检查列是否存在
    const [results] = await sequelize.query(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'style_prompts'
        AND COLUMN_NAME = 'example_html'
    `);
    
    // 如果列不存在，才添加列
    if (results.length === 0) {
      await sequelize.query("ALTER TABLE style_prompts ADD COLUMN example_html TEXT COMMENT 'HTML示例'");
      console.log('新增example_html列成功！');
    } else {
      console.log('example_html列已经存在，无需添加。');
    }
  } catch (err) {
    console.error('数据库操作失败:', err);
  } finally {
    process.exit();
  }
}

addColumn(); 